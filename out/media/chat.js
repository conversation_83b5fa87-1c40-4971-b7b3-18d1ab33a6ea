// 测试助手对话面板JavaScript

(function() {
    const vscode = acquireVsCodeApi();
    
    // DOM元素
    const messageInput = document.getElementById('messageInput');
    const sendBtn = document.getElementById('sendBtn');
    const chatMessages = document.getElementById('chatMessages');
    const thinkingIndicator = document.getElementById('thinkingIndicator');
    const analyzeBtn = document.getElementById('analyzeBtn');
    const testBtn = document.getElementById('testBtn');
    const clearBtn = document.getElementById('clearBtn');

    // 初始化
    function init() {
        setupEventListeners();
        messageInput.focus();
    }

    // 设置事件监听器
    function setupEventListeners() {
        // 发送按钮点击
        sendBtn.addEventListener('click', sendMessage);
        
        // 输入框回车发送（Ctrl+Enter或Shift+Enter换行）
        messageInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey && !e.ctrlKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        // 快捷按钮
        analyzeBtn.addEventListener('click', () => {
            vscode.postMessage({ command: 'analyzeCurrentCode' });
        });

        testBtn.addEventListener('click', () => {
            vscode.postMessage({ command: 'generateTest' });
        });

        clearBtn.addEventListener('click', () => {
            if (confirm('确定要清除所有对话历史吗？')) {
                vscode.postMessage({ command: 'clearHistory' });
            }
        });

        // 监听来自扩展的消息
        window.addEventListener('message', handleExtensionMessage);
    }

    // 发送消息
    function sendMessage() {
        const text = messageInput.value.trim();
        if (!text) return;

        // 清空输入框
        messageInput.value = '';
        
        // 发送消息到扩展
        vscode.postMessage({
            command: 'sendMessage',
            text: text
        });
    }

    // 处理来自扩展的消息
    function handleExtensionMessage(event) {
        const message = event.data;
        
        switch (message.command) {
            case 'updateMessages':
                updateMessages(message.messages);
                break;
            case 'showThinking':
                showThinking();
                break;
            case 'hideThinking':
                hideThinking();
                break;
        }
    }

    // 更新消息显示
    function updateMessages(messages) {
        // 清除欢迎消息
        const welcomeMessage = chatMessages.querySelector('.welcome-message');
        if (welcomeMessage && messages.length > 0) {
            welcomeMessage.remove();
        }

        // 清空现有消息
        const existingMessages = chatMessages.querySelectorAll('.message');
        existingMessages.forEach(msg => msg.remove());

        // 添加所有消息
        messages.forEach(message => {
            addMessageToDOM(message);
        });

        // 滚动到底部
        scrollToBottom();
    }

    // 添加消息到DOM
    function addMessageToDOM(message) {
        const messageElement = document.createElement('div');
        messageElement.className = `message ${message.type}`;
        messageElement.setAttribute('data-id', message.id);

        const avatar = getAvatarForType(message.type);
        const time = new Date(message.timestamp).toLocaleTimeString();
        
        messageElement.innerHTML = `
            <div class="message-header">
                <div class="message-avatar ${message.type}">${avatar}</div>
                <span class="message-time">${time}</span>
            </div>
            <div class="message-content">${formatMessageContent(message.content)}</div>
        `;

        chatMessages.appendChild(messageElement);
    }

    // 获取消息类型对应的头像
    function getAvatarForType(type) {
        switch (type) {
            case 'user':
                return '👤';
            case 'assistant':
                return '🤖';
            case 'system':
                return 'ℹ️';
            case 'error':
                return '❌';
            default:
                return '💬';
        }
    }

    // 格式化消息内容
    function formatMessageContent(content) {
        // 转义HTML
        content = escapeHtml(content);
        
        // 处理代码块
        content = content.replace(/```(\w+)?\n([\s\S]*?)\n```/g, (match, lang, code) => {
            return `<pre><code class="language-${lang || ''}">${code}</code></pre>`;
        });
        
        // 处理行内代码
        content = content.replace(/`([^`]+)`/g, '<code>$1</code>');
        
        // 处理换行
        content = content.replace(/\n/g, '<br>');
        
        // 处理链接
        content = content.replace(/(https?:\/\/[^\s]+)/g, '<a href="$1" target="_blank">$1</a>');
        
        return content;
    }

    // 转义HTML
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // 显示思考指示器
    function showThinking() {
        thinkingIndicator.style.display = 'flex';
        sendBtn.disabled = true;
        sendBtn.textContent = '思考中...';
        scrollToBottom();
    }

    // 隐藏思考指示器
    function hideThinking() {
        thinkingIndicator.style.display = 'none';
        sendBtn.disabled = false;
        sendBtn.textContent = '发送';
        messageInput.focus();
    }

    // 滚动到底部
    function scrollToBottom() {
        setTimeout(() => {
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }, 100);
    }

    // 添加快捷键支持
    document.addEventListener('keydown', (e) => {
        // Ctrl/Cmd + K 清除历史
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            if (confirm('确定要清除所有对话历史吗？')) {
                vscode.postMessage({ command: 'clearHistory' });
            }
        }
        
        // Ctrl/Cmd + 1 分析代码
        if ((e.ctrlKey || e.metaKey) && e.key === '1') {
            e.preventDefault();
            vscode.postMessage({ command: 'analyzeCurrentCode' });
        }
        
        // Ctrl/Cmd + 2 生成测试
        if ((e.ctrlKey || e.metaKey) && e.key === '2') {
            e.preventDefault();
            vscode.postMessage({ command: 'generateTest' });
        }
    });

    // 添加右键菜单支持
    chatMessages.addEventListener('contextmenu', (e) => {
        e.preventDefault();
        
        const messageElement = e.target.closest('.message');
        if (messageElement) {
            showContextMenu(e, messageElement);
        }
    });

    // 显示右键菜单
    function showContextMenu(event, messageElement) {
        const menu = document.createElement('div');
        menu.className = 'context-menu';
        menu.style.position = 'fixed';
        menu.style.left = event.clientX + 'px';
        menu.style.top = event.clientY + 'px';
        menu.style.backgroundColor = 'var(--vscode-menu-background)';
        menu.style.border = '1px solid var(--vscode-menu-border)';
        menu.style.borderRadius = '4px';
        menu.style.padding = '4px 0';
        menu.style.zIndex = '1000';
        menu.style.minWidth = '120px';
        menu.style.boxShadow = '0 2px 8px rgba(0,0,0,0.15)';

        const copyItem = document.createElement('div');
        copyItem.textContent = '复制消息';
        copyItem.style.padding = '8px 16px';
        copyItem.style.cursor = 'pointer';
        copyItem.style.color = 'var(--vscode-menu-foreground)';
        copyItem.addEventListener('click', () => {
            const content = messageElement.querySelector('.message-content').textContent;
            navigator.clipboard.writeText(content);
            menu.remove();
        });
        copyItem.addEventListener('mouseenter', () => {
            copyItem.style.backgroundColor = 'var(--vscode-menu-selectionBackground)';
        });
        copyItem.addEventListener('mouseleave', () => {
            copyItem.style.backgroundColor = 'transparent';
        });

        menu.appendChild(copyItem);
        document.body.appendChild(menu);

        // 点击其他地方关闭菜单
        const closeMenu = (e) => {
            if (!menu.contains(e.target)) {
                menu.remove();
                document.removeEventListener('click', closeMenu);
            }
        };
        setTimeout(() => {
            document.addEventListener('click', closeMenu);
        }, 0);
    }

    // 添加拖拽文件支持
    chatMessages.addEventListener('dragover', (e) => {
        e.preventDefault();
        chatMessages.style.backgroundColor = 'var(--vscode-list-dropBackground)';
    });

    chatMessages.addEventListener('dragleave', (e) => {
        e.preventDefault();
        chatMessages.style.backgroundColor = '';
    });

    chatMessages.addEventListener('drop', (e) => {
        e.preventDefault();
        chatMessages.style.backgroundColor = '';
        
        const files = Array.from(e.dataTransfer.files);
        if (files.length > 0) {
            const file = files[0];
            if (file.type.startsWith('text/') || file.name.match(/\.(js|ts|py|java|cs|cpp|c|php|rb|go|html|css|json|xml|md)$/i)) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    messageInput.value = `请分析这个文件的代码：\n\n文件名：${file.name}\n\n代码内容：\n\`\`\`\n${e.target.result}\n\`\`\``;
                    messageInput.focus();
                };
                reader.readAsText(file);
            } else {
                alert('请拖拽文本文件或代码文件');
            }
        }
    });

    // 初始化应用
    init();
})();
