/* 测试助手对话面板样式 */
:root {
    --vscode-font-family: var(--vscode-font-family);
    --primary-color: #007acc;
    --success-color: #28a745;
    --error-color: #dc3545;
    --warning-color: #ffc107;
    --border-radius: 6px;
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
}

* {
    box-sizing: border-box;
}

body {
    font-family: var(--vscode-font-family);
    margin: 0;
    padding: 0;
    background-color: var(--vscode-editor-background);
    color: var(--vscode-editor-foreground);
    height: 100vh;
    overflow: hidden;
}

.chat-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
}

/* 头部样式 */
.chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--vscode-panel-border);
    background-color: var(--vscode-panel-background);
}

.chat-header h3 {
    margin: 0;
    color: var(--vscode-foreground);
    font-size: 16px;
    font-weight: 600;
}

.header-buttons {
    display: flex;
    gap: var(--spacing-sm);
}

.header-btn {
    padding: var(--spacing-xs) var(--spacing-sm);
    border: 1px solid var(--vscode-button-border);
    background-color: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s ease;
}

.header-btn:hover {
    background-color: var(--vscode-button-hoverBackground);
}

.header-btn:active {
    transform: translateY(1px);
}

/* 消息区域样式 */
.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-md);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.welcome-message {
    background-color: var(--vscode-textBlockQuote-background);
    border-left: 4px solid var(--primary-color);
    padding: var(--spacing-md);
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing-lg);
}

.welcome-message p {
    margin: 0 0 var(--spacing-sm) 0;
    color: var(--vscode-foreground);
}

.welcome-message ul {
    margin: var(--spacing-sm) 0;
    padding-left: var(--spacing-lg);
}

.welcome-message li {
    margin: var(--spacing-xs) 0;
    color: var(--vscode-foreground);
}

.message {
    display: flex;
    flex-direction: column;
    margin-bottom: var(--spacing-md);
    animation: fadeIn 0.3s ease-in;
}

.message-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-xs);
}

.message-avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
}

.message-avatar.user {
    background-color: var(--primary-color);
    color: white;
}

.message-avatar.assistant {
    background-color: var(--success-color);
    color: white;
}

.message-avatar.error {
    background-color: var(--error-color);
    color: white;
}

.message-time {
    font-size: 11px;
    color: var(--vscode-descriptionForeground);
}

.message-content {
    background-color: var(--vscode-input-background);
    border: 1px solid var(--vscode-input-border);
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    margin-left: 32px;
    white-space: pre-wrap;
    word-wrap: break-word;
    line-height: 1.5;
}

.message.user .message-content {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.message.error .message-content {
    background-color: var(--error-color);
    color: white;
    border-color: var(--error-color);
}

.message-content code {
    background-color: var(--vscode-textCodeBlock-background);
    padding: 2px 4px;
    border-radius: 3px;
    font-family: var(--vscode-editor-font-family);
    font-size: 13px;
}

.message-content pre {
    background-color: var(--vscode-textCodeBlock-background);
    border: 1px solid var(--vscode-panel-border);
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    overflow-x: auto;
    margin: var(--spacing-sm) 0;
}

.message-content pre code {
    background: none;
    padding: 0;
}

/* 思考指示器样式 */
.thinking-indicator {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    background-color: var(--vscode-panel-background);
    border-top: 1px solid var(--vscode-panel-border);
    color: var(--vscode-descriptionForeground);
    font-size: 14px;
}

.thinking-dots {
    display: flex;
    gap: 4px;
}

.thinking-dots span {
    width: 6px;
    height: 6px;
    background-color: var(--primary-color);
    border-radius: 50%;
    animation: thinking 1.4s infinite ease-in-out both;
}

.thinking-dots span:nth-child(1) { animation-delay: -0.32s; }
.thinking-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes thinking {
    0%, 80%, 100% {
        transform: scale(0);
    }
    40% {
        transform: scale(1);
    }
}

/* 输入区域样式 */
.chat-input {
    border-top: 1px solid var(--vscode-panel-border);
    background-color: var(--vscode-panel-background);
    padding: var(--spacing-md);
}

.input-container {
    display: flex;
    gap: var(--spacing-sm);
    align-items: flex-end;
}

#messageInput {
    flex: 1;
    background-color: var(--vscode-input-background);
    border: 1px solid var(--vscode-input-border);
    color: var(--vscode-input-foreground);
    border-radius: var(--border-radius);
    padding: var(--spacing-sm);
    font-family: var(--vscode-font-family);
    font-size: 14px;
    resize: vertical;
    min-height: 60px;
    max-height: 120px;
}

#messageInput:focus {
    outline: none;
    border-color: var(--vscode-focusBorder);
}

#messageInput::placeholder {
    color: var(--vscode-input-placeholderForeground);
}

.send-btn {
    padding: var(--spacing-sm) var(--spacing-md);
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
    height: 40px;
}

.send-btn:hover {
    background-color: #005a9e;
}

.send-btn:disabled {
    background-color: var(--vscode-button-secondaryBackground);
    color: var(--vscode-button-secondaryForeground);
    cursor: not-allowed;
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 滚动条样式 */
.chat-messages::-webkit-scrollbar {
    width: 8px;
}

.chat-messages::-webkit-scrollbar-track {
    background: var(--vscode-scrollbarSlider-background);
}

.chat-messages::-webkit-scrollbar-thumb {
    background: var(--vscode-scrollbarSlider-background);
    border-radius: 4px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: var(--vscode-scrollbarSlider-hoverBackground);
}

/* 响应式设计 */
@media (max-width: 600px) {
    .chat-header {
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: stretch;
    }
    
    .header-buttons {
        justify-content: space-between;
    }
    
    .header-btn {
        flex: 1;
        text-align: center;
    }
}
