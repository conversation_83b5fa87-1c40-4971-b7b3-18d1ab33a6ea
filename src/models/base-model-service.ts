import { Disposable } from 'vscode';

/**
 * 模型响应接口
 */
export interface ModelResponse {
    success: boolean;
    content?: string;
    error?: string;
    raw_response?: any;
}

/**
 * 模型配置接口
 */
export interface ModelConfig {
    [key: string]: any;
}

/**
 * 模型信息接口
 */
export interface ModelInfo {
    model_type: string;
    model_name: string;
    provider: string;
    is_available: boolean;
    description: string;
    [key: string]: any;
}

/**
 * 基础模型服务抽象类
 */
export abstract class BaseModelService implements Disposable {
    protected model_type: string;
    protected config: ModelConfig;
    protected is_initialized: boolean = false;

    constructor(model_type: string, config: ModelConfig = {}) {
        this.model_type = model_type;
        this.config = config || {};
    }

    /**
     * 初始化模型服务
     */
    abstract initialize(): Promise<boolean>;

    /**
     * 检查模型服务是否可用
     */
    abstract is_available(): Promise<boolean>;

    /**
     * 获取模型信息
     */
    abstract get_model_info(): ModelInfo;

    /**
     * 调用大语言模型
     */
    protected abstract _call_llm(prompt: string): Promise<ModelResponse>;

    /**
     * 设置配置
     */
    abstract set_config(config: ModelConfig): void;

    /**
     * 获取配置
     */
    abstract get_config(): ModelConfig;

    /**
     * 代码理解功能
     */
    async understand_code(code: string, language: string, context?: string): Promise<ModelResponse> {
        const prompt = this._build_code_understanding_prompt(code, language, context);
        return await this._call_llm(prompt);
    }

    /**
     * 生成测试建议
     */
    async generate_test_suggestions(code: string, language: string, test_type?: string): Promise<ModelResponse> {
        const prompt = this._build_test_suggestion_prompt(code, language, test_type);
        return await this._call_llm(prompt);
    }

    /**
     * 错误修复建议
     */
    async suggest_error_fix(code: string, error_message: string, language: string): Promise<ModelResponse> {
        const prompt = this._build_error_fix_prompt(code, error_message, language);
        return await this._call_llm(prompt);
    }

    /**
     * 接口测试建议
     */
    async suggest_interface_test(interface_code: string, language: string): Promise<ModelResponse> {
        const prompt = this._build_interface_test_prompt(interface_code, language);
        return await this._call_llm(prompt);
    }

    /**
     * 准备测试数据
     */
    async prepare_test_data(code: string, language: string, data_type?: string): Promise<ModelResponse> {
        const prompt = this._build_test_data_prompt(code, language, data_type);
        return await this._call_llm(prompt);
    }

    /**
     * 构建代码理解提示词
     */
    protected _build_code_understanding_prompt(code: string, language: string, context?: string): string {
        let prompt = `请分析以下${language}代码，提供详细的代码理解和说明：\n\n`;
        
        if (context) {
            prompt += `上下文信息：\n${context}\n\n`;
        }
        
        prompt += `代码：\n\`\`\`${language}\n${code}\n\`\`\`\n\n`;
        prompt += `请从以下几个方面进行分析：\n`;
        prompt += `1. 代码功能和目的\n`;
        prompt += `2. 主要逻辑流程\n`;
        prompt += `3. 关键变量和方法\n`;
        prompt += `4. 潜在的问题或改进建议\n`;
        prompt += `5. 代码质量评估\n`;
        
        return prompt;
    }

    /**
     * 构建测试建议提示词
     */
    protected _build_test_suggestion_prompt(code: string, language: string, test_type?: string): string {
        let prompt = `请为以下${language}代码生成测试建议：\n\n`;
        prompt += `代码：\n\`\`\`${language}\n${code}\n\`\`\`\n\n`;
        
        if (test_type) {
            prompt += `测试类型：${test_type}\n\n`;
        }
        
        prompt += `请提供：\n`;
        prompt += `1. 单元测试用例设计\n`;
        prompt += `2. 边界条件测试\n`;
        prompt += `3. 异常情况测试\n`;
        prompt += `4. 性能测试建议\n`;
        prompt += `5. 具体的测试代码示例\n`;
        
        return prompt;
    }

    /**
     * 构建错误修复提示词
     */
    protected _build_error_fix_prompt(code: string, error_message: string, language: string): string {
        let prompt = `请分析以下${language}代码中的错误并提供修复建议：\n\n`;
        prompt += `错误信息：\n${error_message}\n\n`;
        prompt += `代码：\n\`\`\`${language}\n${code}\n\`\`\`\n\n`;
        prompt += `请提供：\n`;
        prompt += `1. 错误原因分析\n`;
        prompt += `2. 具体修复方案\n`;
        prompt += `3. 修复后的代码\n`;
        prompt += `4. 预防类似错误的建议\n`;
        
        return prompt;
    }

    /**
     * 构建接口测试提示词
     */
    protected _build_interface_test_prompt(interface_code: string, language: string): string {
        let prompt = `请为以下${language}接口生成测试建议：\n\n`;
        prompt += `接口代码：\n\`\`\`${language}\n${interface_code}\n\`\`\`\n\n`;
        prompt += `请提供：\n`;
        prompt += `1. 接口功能分析\n`;
        prompt += `2. 输入参数验证测试\n`;
        prompt += `3. 返回值验证测试\n`;
        prompt += `4. 异常处理测试\n`;
        prompt += `5. 性能和压力测试建议\n`;
        prompt += `6. 具体的测试用例和测试代码\n`;
        
        return prompt;
    }

    /**
     * 构建测试数据提示词
     */
    protected _build_test_data_prompt(code: string, language: string, data_type?: string): string {
        let prompt = `请为以下${language}代码生成测试数据：\n\n`;
        prompt += `代码：\n\`\`\`${language}\n${code}\n\`\`\`\n\n`;
        
        if (data_type) {
            prompt += `数据类型：${data_type}\n\n`;
        }
        
        prompt += `请提供：\n`;
        prompt += `1. 正常情况的测试数据\n`;
        prompt += `2. 边界值测试数据\n`;
        prompt += `3. 异常情况测试数据\n`;
        prompt += `4. 性能测试数据\n`;
        prompt += `5. 数据格式和结构说明\n`;
        
        return prompt;
    }

    /**
     * 创建错误响应
     */
    protected _create_error_response(error_message: string): ModelResponse {
        return {
            success: false,
            error: error_message,
            content: undefined
        };
    }

    /**
     * 解析LLM响应
     */
    protected _parse_llm_response(content: string): ModelResponse {
        try {
            // 尝试解析JSON格式的响应
            const parsed = JSON.parse(content);
            return {
                success: true,
                content: parsed.content || content,
                raw_response: parsed
            };
        } catch {
            // 如果不是JSON格式，直接返回文本内容
            return {
                success: true,
                content: content
            };
        }
    }

    /**
     * 清理资源
     */
    dispose(): void {
        // 子类可以重写此方法来清理特定资源
    }
}
