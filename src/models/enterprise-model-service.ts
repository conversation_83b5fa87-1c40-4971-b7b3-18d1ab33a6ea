import axios, { AxiosResponse } from 'axios';
import * as crypto from 'crypto';
import { BaseModelService, ModelResponse, ModelConfig, ModelInfo } from './base-model-service';

/**
 * 企业模型配置接口
 */
export interface EnterpriseModelConfig extends ModelConfig {
    url?: string;
    app_id?: string;
    username?: string;
    api_secret?: string;
    timeout?: number;
    max_retries?: number;
}

/**
 * 企业模型服务类
 */
export class EnterpriseModelService extends BaseModelService {
    private url: string;
    private app_id: string;
    private username: string;
    private api_secret: string;
    private timeout: number;
    private max_retries: number;

    constructor(config: EnterpriseModelConfig = {}) {
        super("enterprise", config);
        this.url = config.url || 'http://inmmcuat.xiaopuuat.com:13600/reflect/mmc/llm/app/chat/completion';
        this.app_id = config.app_id || 'app_20250709104215697_908735';
        this.username = config.username || 'uatgw06541';
        this.api_secret = config.api_secret || 'edf35acd3d9c4f66b82a1398e3ec769e';
        this.timeout = config.timeout || 30000; // 毫秒
        this.max_retries = config.max_retries || 3;
    }

    /**
     * 初始化企业模型服务
     */
    async initialize(): Promise<boolean> {
        try {
            // 验证必要的配置参数
            if (!this.url || !this.app_id || !this.username || !this.api_secret) {
                console.error('企业模型服务配置不完整');
                return false;
            }

            // 测试连接
            const test_response = await this._test_connection();
            this.is_initialized = test_response;
            return this.is_initialized;

        } catch (error) {
            console.error(`企业模型服务初始化失败: ${error}`);
            return false;
        }
    }

    /**
     * 检查企业模型服务是否可用
     */
    async is_available(): Promise<boolean> {
        if (!this.is_initialized) {
            return await this.initialize();
        }
        return await this._test_connection();
    }

    /**
     * 获取企业模型信息
     */
    get_model_info(): ModelInfo {
        return {
            model_type: "enterprise",
            model_name: "企业内部大模型",
            provider: "Enterprise Internal",
            url: this.url,
            app_id: this.app_id,
            username: this.username,
            is_available: this.is_initialized,
            description: "企业内部部署的大语言模型服务"
        };
    }

    /**
     * 测试企业模型连接
     */
    private async _test_connection(): Promise<boolean> {
        try {
            const test_query = "测试连接";
            const response = await this._make_request(test_query);
            return response.success;
        } catch {
            return false;
        }
    }

    /**
     * MD5加密
     */
    private _md5_encrypt(text: string): string {
        return crypto.createHash('md5').update(text, 'utf8').digest('hex');
    }

    /**
     * 生成签名
     */
    private _generate_signature(query: string): string {
        const signature_text = this.username + query + this.api_secret;
        return this._md5_encrypt(signature_text);
    }

    /**
     * 发送请求到企业模型
     */
    private async _make_request(query: string): Promise<ModelResponse> {
        const data = {
            query: query,
            history: [],
            stream: false, // 改为非流式以简化处理
            chat_type: "sync",
            app_id: this.app_id,
            username: this.username
        };

        const signature = this._generate_signature(query);

        const headers = {
            "Content-Type": "application/json; charset=UTF-8",
            "x-api-key": "UTMP",
            "x-signature": signature
        };

        for (let attempt = 0; attempt < this.max_retries; attempt++) {
            try {
                const response: AxiosResponse = await axios.post(
                    this.url,
                    data,
                    {
                        headers: headers,
                        timeout: this.timeout
                    }
                );

                if (response.status === 200) {
                    // 处理企业模型的响应格式
                    const response_text = response.data;

                    let content: string;
                    // 如果是流式响应，需要解析SSE格式
                    if (typeof response_text === 'string' && 
                        (response_text.includes("event:") && response_text.includes("data:"))) {
                        content = this._parse_sse_response(response_text);
                    } else {
                        // 非流式响应，直接解析JSON
                        try {
                            if (typeof response_text === 'object' && response_text.content) {
                                content = response_text.content;
                            } else {
                                content = typeof response_text === 'string' ? response_text : JSON.stringify(response_text);
                            }
                        } catch {
                            content = typeof response_text === 'string' ? response_text : JSON.stringify(response_text);
                        }
                    }

                    return {
                        success: true,
                        content: content,
                        raw_response: response_text
                    };
                } else {
                    const error_msg = `HTTP ${response.status}: ${response.statusText}`;
                    if (attempt === this.max_retries - 1) {
                        return {
                            success: false,
                            error: error_msg,
                            content: undefined
                        };
                    }
                    await this._sleep(1000); // 重试前等待
                }

            } catch (error: any) {
                let error_msg: string;
                
                if (error.code === 'ECONNABORTED') {
                    error_msg = `请求超时 (尝试 ${attempt + 1}/${this.max_retries})`;
                } else if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
                    error_msg = `连接失败 (尝试 ${attempt + 1}/${this.max_retries})`;
                } else {
                    error_msg = `请求异常: ${error.message}`;
                }

                if (attempt === this.max_retries - 1) {
                    return {
                        success: false,
                        error: error_msg,
                        content: undefined
                    };
                }
                
                await this._sleep(error.code === 'ECONNREFUSED' ? 2000 : 1000);
            }
        }

        return {
            success: false,
            error: "达到最大重试次数",
            content: undefined
        };
    }

    /**
     * 解析SSE流式响应
     */
    private _parse_sse_response(response_text: string): string {
        try {
            const lines = response_text.trim().split('\n');
            const content_parts: string[] = [];

            for (const line of lines) {
                if (line.startsWith('data: ')) {
                    const data_json = line.substring(6); // 移除 'data: ' 前缀
                    try {
                        const data = JSON.parse(data_json);
                        if (data.body && data.body.data && data.body.data.content) {
                            content_parts.push(data.body.data.content);
                        }
                    } catch {
                        continue;
                    }
                }
            }

            return content_parts.join('');
        } catch {
            return response_text;
        }
    }

    /**
     * 调用企业内部大语言模型
     */
    protected async _call_llm(prompt: string): Promise<ModelResponse> {
        try {
            if (!(await this.is_available())) {
                return this._create_error_response("企业模型服务不可用");
            }

            // 发送请求
            const response = await this._make_request(prompt);

            if (!response.success) {
                const error_msg = response.error || '未知错误';
                return this._create_error_response(error_msg);
            }

            // 解析响应内容
            const content = response.content || '';
            if (!content) {
                return this._create_error_response("模型返回空内容");
            }

            // 解析为JSON格式
            return this._parse_llm_response(content);

        } catch (error: any) {
            return this._create_error_response(`调用企业模型失败: ${error.message}`);
        }
    }

    /**
     * 更新企业模型配置
     */
    set_config(config: EnterpriseModelConfig): void {
        Object.assign(this.config, config);
        this.url = config.url || this.url;
        this.app_id = config.app_id || this.app_id;
        this.username = config.username || this.username;
        this.api_secret = config.api_secret || this.api_secret;
        this.timeout = config.timeout || this.timeout;
        this.max_retries = config.max_retries || this.max_retries;

        // 重新初始化
        this.is_initialized = false;
        this.initialize();
    }

    /**
     * 获取当前配置（不包含敏感信息）
     */
    get_config(): EnterpriseModelConfig {
        return {
            url: this.url,
            app_id: this.app_id,
            username: this.username,
            timeout: this.timeout,
            max_retries: this.max_retries,
            model_type: this.model_type
        };
    }

    /**
     * 睡眠函数
     */
    private _sleep(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 清理资源
     */
    dispose(): void {
        // 清理企业模型服务相关资源
        super.dispose();
    }
}
