import * as vscode from 'vscode';
import { BaseModelService, ModelResponse } from '../models/base-model-service';
import { EnterpriseModelService } from '../models/enterprise-model-service';

/**
 * 代码分析结果接口
 */
export interface CodeAnalysisResult {
    success: boolean;
    analysis?: string;
    suggestions?: string[];
    error?: string;
}

/**
 * 测试建议结果接口
 */
export interface TestSuggestionResult {
    success: boolean;
    test_cases?: string[];
    test_code?: string;
    suggestions?: string[];
    error?: string;
}

/**
 * 错误修复结果接口
 */
export interface ErrorFixResult {
    success: boolean;
    analysis?: string;
    fix_suggestions?: string[];
    fixed_code?: string;
    error?: string;
}

/**
 * 测试助手核心服务类
 */
export class TestAssistantService {
    private modelService: BaseModelService;
    private context: vscode.ExtensionContext;

    constructor(context: vscode.ExtensionContext) {
        this.context = context;
        this.modelService = this.createModelService();
    }

    /**
     * 创建模型服务实例
     */
    private createModelService(): BaseModelService {
        const config = vscode.workspace.getConfiguration('testAssistant.enterprise');
        
        return new EnterpriseModelService({
            url: config.get('url', ''),
            app_id: config.get('appId', ''),
            username: config.get('username', ''),
            api_secret: config.get('apiSecret', ''),
            timeout: config.get('timeout', 30) * 1000, // 转换为毫秒
            max_retries: config.get('maxRetries', 3)
        });
    }

    /**
     * 初始化服务
     */
    async initialize(): Promise<boolean> {
        try {
            const initialized = await this.modelService.initialize();
            if (!initialized) {
                vscode.window.showErrorMessage('企业模型服务初始化失败，请检查配置');
                return false;
            }
            return true;
        } catch (error) {
            vscode.window.showErrorMessage(`服务初始化失败: ${error}`);
            return false;
        }
    }

    /**
     * 分析代码
     */
    async analyzeCode(code: string, language: string, context?: string): Promise<CodeAnalysisResult> {
        try {
            if (!await this.modelService.is_available()) {
                return {
                    success: false,
                    error: '模型服务不可用'
                };
            }

            const response = await this.modelService.understand_code(code, language, context);
            
            if (!response.success) {
                return {
                    success: false,
                    error: response.error || '代码分析失败'
                };
            }

            // 解析分析结果
            const analysis = response.content || '';
            const suggestions = this.extractSuggestions(analysis);

            return {
                success: true,
                analysis: analysis,
                suggestions: suggestions
            };

        } catch (error) {
            return {
                success: false,
                error: `代码分析异常: ${error}`
            };
        }
    }

    /**
     * 生成测试建议
     */
    async generateTestSuggestions(code: string, language: string, testType?: string): Promise<TestSuggestionResult> {
        try {
            if (!await this.modelService.is_available()) {
                return {
                    success: false,
                    error: '模型服务不可用'
                };
            }

            const response = await this.modelService.generate_test_suggestions(code, language, testType);
            
            if (!response.success) {
                return {
                    success: false,
                    error: response.error || '测试建议生成失败'
                };
            }

            const content = response.content || '';
            const testCases = this.extractTestCases(content);
            const testCode = this.extractTestCode(content);
            const suggestions = this.extractSuggestions(content);

            return {
                success: true,
                test_cases: testCases,
                test_code: testCode,
                suggestions: suggestions
            };

        } catch (error) {
            return {
                success: false,
                error: `测试建议生成异常: ${error}`
            };
        }
    }

    /**
     * 修复错误
     */
    async fixError(code: string, errorMessage: string, language: string): Promise<ErrorFixResult> {
        try {
            if (!await this.modelService.is_available()) {
                return {
                    success: false,
                    error: '模型服务不可用'
                };
            }

            const response = await this.modelService.suggest_error_fix(code, errorMessage, language);
            
            if (!response.success) {
                return {
                    success: false,
                    error: response.error || '错误修复建议生成失败'
                };
            }

            const content = response.content || '';
            const analysis = this.extractErrorAnalysis(content);
            const fixSuggestions = this.extractFixSuggestions(content);
            const fixedCode = this.extractFixedCode(content);

            return {
                success: true,
                analysis: analysis,
                fix_suggestions: fixSuggestions,
                fixed_code: fixedCode
            };

        } catch (error) {
            return {
                success: false,
                error: `错误修复异常: ${error}`
            };
        }
    }

    /**
     * 接口测试建议
     */
    async suggestInterfaceTest(interfaceCode: string, language: string): Promise<TestSuggestionResult> {
        try {
            if (!await this.modelService.is_available()) {
                return {
                    success: false,
                    error: '模型服务不可用'
                };
            }

            const response = await this.modelService.suggest_interface_test(interfaceCode, language);
            
            if (!response.success) {
                return {
                    success: false,
                    error: response.error || '接口测试建议生成失败'
                };
            }

            const content = response.content || '';
            const testCases = this.extractTestCases(content);
            const testCode = this.extractTestCode(content);
            const suggestions = this.extractSuggestions(content);

            return {
                success: true,
                test_cases: testCases,
                test_code: testCode,
                suggestions: suggestions
            };

        } catch (error) {
            return {
                success: false,
                error: `接口测试建议异常: ${error}`
            };
        }
    }

    /**
     * 准备测试数据
     */
    async prepareTestData(code: string, language: string, dataType?: string): Promise<ModelResponse> {
        try {
            if (!await this.modelService.is_available()) {
                return {
                    success: false,
                    error: '模型服务不可用'
                };
            }

            return await this.modelService.prepare_test_data(code, language, dataType);

        } catch (error) {
            return {
                success: false,
                error: `测试数据准备异常: ${error}`
            };
        }
    }

    /**
     * 获取当前活动编辑器的代码和语言
     */
    getCurrentEditorInfo(): { code: string; language: string; selection?: string } | null {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            return null;
        }

        const document = editor.document;
        const selection = editor.selection;
        
        let code: string;
        let selectedText: string | undefined;

        if (!selection.isEmpty) {
            selectedText = document.getText(selection);
            code = selectedText;
        } else {
            code = document.getText();
        }

        return {
            code: code,
            language: document.languageId,
            selection: selectedText
        };
    }

    /**
     * 提取建议列表
     */
    private extractSuggestions(content: string): string[] {
        const suggestions: string[] = [];
        const lines = content.split('\n');
        
        for (const line of lines) {
            const trimmed = line.trim();
            if (trimmed.match(/^\d+\.\s+/) || trimmed.startsWith('- ') || trimmed.startsWith('* ')) {
                suggestions.push(trimmed);
            }
        }
        
        return suggestions;
    }

    /**
     * 提取测试用例
     */
    private extractTestCases(content: string): string[] {
        const testCases: string[] = [];
        const lines = content.split('\n');
        let inTestCaseSection = false;
        
        for (const line of lines) {
            const trimmed = line.trim();
            if (trimmed.includes('测试用例') || trimmed.includes('test case')) {
                inTestCaseSection = true;
                continue;
            }
            
            if (inTestCaseSection && (trimmed.match(/^\d+\.\s+/) || trimmed.startsWith('- '))) {
                testCases.push(trimmed);
            }
        }
        
        return testCases;
    }

    /**
     * 提取测试代码
     */
    private extractTestCode(content: string): string {
        const codeBlockRegex = /```[\w]*\n([\s\S]*?)\n```/g;
        const matches = content.match(codeBlockRegex);
        
        if (matches && matches.length > 0) {
            // 返回最后一个代码块，通常是完整的测试代码
            return matches[matches.length - 1].replace(/```[\w]*\n/, '').replace(/\n```$/, '');
        }
        
        return '';
    }

    /**
     * 提取错误分析
     */
    private extractErrorAnalysis(content: string): string {
        const lines = content.split('\n');
        const analysisLines: string[] = [];
        let inAnalysisSection = false;
        
        for (const line of lines) {
            const trimmed = line.trim();
            if (trimmed.includes('错误分析') || trimmed.includes('原因分析')) {
                inAnalysisSection = true;
                continue;
            }
            
            if (inAnalysisSection && trimmed.includes('修复')) {
                break;
            }
            
            if (inAnalysisSection && trimmed) {
                analysisLines.push(trimmed);
            }
        }
        
        return analysisLines.join('\n');
    }

    /**
     * 提取修复建议
     */
    private extractFixSuggestions(content: string): string[] {
        const suggestions: string[] = [];
        const lines = content.split('\n');
        let inFixSection = false;
        
        for (const line of lines) {
            const trimmed = line.trim();
            if (trimmed.includes('修复') || trimmed.includes('解决方案')) {
                inFixSection = true;
                continue;
            }
            
            if (inFixSection && (trimmed.match(/^\d+\.\s+/) || trimmed.startsWith('- '))) {
                suggestions.push(trimmed);
            }
        }
        
        return suggestions;
    }

    /**
     * 提取修复后的代码
     */
    private extractFixedCode(content: string): string {
        return this.extractTestCode(content); // 使用相同的代码提取逻辑
    }

    /**
     * 更新模型配置
     */
    updateModelConfig(): void {
        this.modelService = this.createModelService();
    }

    /**
     * 获取模型信息
     */
    getModelInfo() {
        return this.modelService.get_model_info();
    }

    /**
     * 清理资源
     */
    dispose(): void {
        this.modelService.dispose();
    }
}
