import * as vscode from 'vscode';
import * as path from 'path';

/**
 * 文件信息接口
 */
export interface FileInfo {
    path: string;
    language: string;
    content: string;
    size: number;
    lastModified: Date;
}

/**
 * 上下文信息接口
 */
export interface ContextInfo {
    currentFile: FileInfo;
    relatedFiles: FileInfo[];
    imports: string[];
    exports: string[];
    dependencies: string[];
    projectStructure: string;
    workspaceInfo: string;
}

/**
 * 上下文分析器类
 */
export class ContextAnalyzer {
    private context: vscode.ExtensionContext;
    private maxFileSize: number;
    private maxRelatedFiles: number = 10;
    private fileCache: Map<string, FileInfo> = new Map();

    constructor(context: vscode.ExtensionContext) {
        this.context = context;
        this.maxFileSize = vscode.workspace.getConfiguration('testAssistant.context').get('maxFileSize', 1048576);
    }

    /**
     * 获取代码的上下文信息
     */
    async getContextForCode(code: string, language: string): Promise<string> {
        try {
            const editor = vscode.window.activeTextEditor;
            if (!editor) {
                return '';
            }

            const contextInfo = await this.analyzeContext(editor.document);
            return this.formatContextInfo(contextInfo);

        } catch (error) {
            console.error('获取上下文信息失败:', error);
            return '';
        }
    }

    /**
     * 分析文档上下文
     */
    private async analyzeContext(document: vscode.TextDocument): Promise<ContextInfo> {
        const currentFile = await this.getFileInfo(document);
        const relatedFiles = await this.findRelatedFiles(document);
        const imports = this.extractImports(document.getText(), document.languageId);
        const exports = this.extractExports(document.getText(), document.languageId);
        const dependencies = await this.analyzeDependencies(document);
        const projectStructure = await this.getProjectStructure();
        const workspaceInfo = this.getWorkspaceInfo();

        return {
            currentFile,
            relatedFiles,
            imports,
            exports,
            dependencies,
            projectStructure,
            workspaceInfo
        };
    }

    /**
     * 获取文件信息
     */
    private async getFileInfo(document: vscode.TextDocument): Promise<FileInfo> {
        const filePath = document.uri.fsPath;
        const stat = await vscode.workspace.fs.stat(document.uri);

        return {
            path: filePath,
            language: document.languageId,
            content: document.getText(),
            size: stat.size,
            lastModified: new Date(stat.mtime)
        };
    }

    /**
     * 查找相关文件
     */
    private async findRelatedFiles(document: vscode.TextDocument): Promise<FileInfo[]> {
        const relatedFiles: FileInfo[] = [];
        const currentDir = path.dirname(document.uri.fsPath);
        const imports = this.extractImports(document.getText(), document.languageId);

        try {
            // 查找同目录下的相关文件
            const files = await vscode.workspace.fs.readDirectory(vscode.Uri.file(currentDir));
            
            for (const [fileName, fileType] of files) {
                if (fileType === vscode.FileType.File && 
                    fileName !== path.basename(document.uri.fsPath) &&
                    this.isCodeFile(fileName)) {
                    
                    const filePath = path.join(currentDir, fileName);
                    const fileUri = vscode.Uri.file(filePath);
                    
                    try {
                        const stat = await vscode.workspace.fs.stat(fileUri);
                        if (stat.size <= this.maxFileSize) {
                            const content = await vscode.workspace.fs.readFile(fileUri);
                            const fileInfo: FileInfo = {
                                path: filePath,
                                language: this.getLanguageFromExtension(fileName),
                                content: Buffer.from(content).toString('utf8'),
                                size: stat.size,
                                lastModified: new Date(stat.mtime)
                            };
                            relatedFiles.push(fileInfo);
                        }
                    } catch (error) {
                        console.warn(`无法读取文件 ${filePath}:`, error);
                    }
                }
            }

            // 查找导入的文件
            for (const importPath of imports) {
                const resolvedPath = await this.resolveImportPath(importPath, currentDir);
                if (resolvedPath && !relatedFiles.some(f => f.path === resolvedPath)) {
                    try {
                        const fileUri = vscode.Uri.file(resolvedPath);
                        const stat = await vscode.workspace.fs.stat(fileUri);
                        
                        if (stat.size <= this.maxFileSize) {
                            const content = await vscode.workspace.fs.readFile(fileUri);
                            const fileInfo: FileInfo = {
                                path: resolvedPath,
                                language: this.getLanguageFromExtension(path.basename(resolvedPath)),
                                content: Buffer.from(content).toString('utf8'),
                                size: stat.size,
                                lastModified: new Date(stat.mtime)
                            };
                            relatedFiles.push(fileInfo);
                        }
                    } catch (error) {
                        console.warn(`无法读取导入文件 ${resolvedPath}:`, error);
                    }
                }
            }

        } catch (error) {
            console.warn('查找相关文件失败:', error);
        }

        return relatedFiles.slice(0, this.maxRelatedFiles);
    }

    /**
     * 提取导入语句
     */
    private extractImports(content: string, language: string): string[] {
        const imports: string[] = [];
        const lines = content.split('\n');

        for (const line of lines) {
            const trimmed = line.trim();
            
            switch (language) {
                case 'javascript':
                case 'typescript':
                    const jsImportMatch = trimmed.match(/^import\s+.*?from\s+['"`]([^'"`]+)['"`]/);
                    const jsRequireMatch = trimmed.match(/require\(['"`]([^'"`]+)['"`]\)/);
                    if (jsImportMatch) imports.push(jsImportMatch[1]);
                    if (jsRequireMatch) imports.push(jsRequireMatch[1]);
                    break;
                    
                case 'python':
                    const pyImportMatch = trimmed.match(/^(?:from\s+(\S+)\s+)?import\s+(.+)/);
                    if (pyImportMatch) {
                        if (pyImportMatch[1]) {
                            imports.push(pyImportMatch[1]);
                        }
                    }
                    break;
                    
                case 'java':
                    const javaImportMatch = trimmed.match(/^import\s+([^;]+);/);
                    if (javaImportMatch) imports.push(javaImportMatch[1]);
                    break;
                    
                case 'csharp':
                    const csUsingMatch = trimmed.match(/^using\s+([^;]+);/);
                    if (csUsingMatch) imports.push(csUsingMatch[1]);
                    break;
            }
        }

        return [...new Set(imports)]; // 去重
    }

    /**
     * 提取导出语句
     */
    private extractExports(content: string, language: string): string[] {
        const exports: string[] = [];
        const lines = content.split('\n');

        for (const line of lines) {
            const trimmed = line.trim();
            
            switch (language) {
                case 'javascript':
                case 'typescript':
                    if (trimmed.startsWith('export ')) {
                        const exportMatch = trimmed.match(/export\s+(?:default\s+)?(?:class|function|const|let|var)\s+(\w+)/);
                        if (exportMatch) exports.push(exportMatch[1]);
                    }
                    break;
                    
                case 'python':
                    if (trimmed.startsWith('def ') || trimmed.startsWith('class ')) {
                        const defMatch = trimmed.match(/(?:def|class)\s+(\w+)/);
                        if (defMatch) exports.push(defMatch[1]);
                    }
                    break;
                    
                case 'java':
                    if (trimmed.includes('public ')) {
                        const publicMatch = trimmed.match(/public\s+(?:static\s+)?(?:class|interface|enum)\s+(\w+)/);
                        if (publicMatch) exports.push(publicMatch[1]);
                    }
                    break;
            }
        }

        return [...new Set(exports)];
    }

    /**
     * 分析依赖关系
     */
    private async analyzeDependencies(document: vscode.TextDocument): Promise<string[]> {
        const dependencies: string[] = [];
        const workspaceFolder = vscode.workspace.getWorkspaceFolder(document.uri);
        
        if (!workspaceFolder) {
            return dependencies;
        }

        try {
            // 查找package.json
            const packageJsonUri = vscode.Uri.joinPath(workspaceFolder.uri, 'package.json');
            try {
                const packageContent = await vscode.workspace.fs.readFile(packageJsonUri);
                const packageJson = JSON.parse(Buffer.from(packageContent).toString('utf8'));
                
                if (packageJson.dependencies) {
                    dependencies.push(...Object.keys(packageJson.dependencies));
                }
                if (packageJson.devDependencies) {
                    dependencies.push(...Object.keys(packageJson.devDependencies));
                }
            } catch {
                // package.json不存在或无法解析
            }

            // 查找requirements.txt (Python)
            const requirementsUri = vscode.Uri.joinPath(workspaceFolder.uri, 'requirements.txt');
            try {
                const requirementsContent = await vscode.workspace.fs.readFile(requirementsUri);
                const lines = Buffer.from(requirementsContent).toString('utf8').split('\n');
                for (const line of lines) {
                    const trimmed = line.trim();
                    if (trimmed && !trimmed.startsWith('#')) {
                        const packageName = trimmed.split(/[>=<]/)[0].trim();
                        dependencies.push(packageName);
                    }
                }
            } catch {
                // requirements.txt不存在
            }

        } catch (error) {
            console.warn('分析依赖关系失败:', error);
        }

        return [...new Set(dependencies)];
    }

    /**
     * 获取项目结构
     */
    private async getProjectStructure(): Promise<string> {
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (!workspaceFolders || workspaceFolders.length === 0) {
            return '';
        }

        try {
            const structure = await this.buildDirectoryTree(workspaceFolders[0].uri, 0, 3);
            return structure;
        } catch (error) {
            console.warn('获取项目结构失败:', error);
            return '';
        }
    }

    /**
     * 构建目录树
     */
    private async buildDirectoryTree(uri: vscode.Uri, depth: number, maxDepth: number): Promise<string> {
        if (depth > maxDepth) {
            return '';
        }

        let tree = '';
        const indent = '  '.repeat(depth);
        
        try {
            const entries = await vscode.workspace.fs.readDirectory(uri);
            
            for (const [name, type] of entries) {
                if (name.startsWith('.') && name !== '.vscode') {
                    continue; // 跳过隐藏文件
                }
                
                tree += `${indent}${name}\n`;
                
                if (type === vscode.FileType.Directory && depth < maxDepth) {
                    const subUri = vscode.Uri.joinPath(uri, name);
                    tree += await this.buildDirectoryTree(subUri, depth + 1, maxDepth);
                }
            }
        } catch (error) {
            console.warn(`无法读取目录 ${uri.fsPath}:`, error);
        }

        return tree;
    }

    /**
     * 获取工作区信息
     */
    private getWorkspaceInfo(): string {
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (!workspaceFolders || workspaceFolders.length === 0) {
            return '无工作区';
        }

        const folder = workspaceFolders[0];
        return `工作区: ${folder.name} (${folder.uri.fsPath})`;
    }

    /**
     * 格式化上下文信息
     */
    private formatContextInfo(contextInfo: ContextInfo): string {
        let context = '';
        
        context += `当前文件: ${path.basename(contextInfo.currentFile.path)} (${contextInfo.currentFile.language})\n`;
        context += `${contextInfo.workspaceInfo}\n\n`;
        
        if (contextInfo.imports.length > 0) {
            context += `导入模块: ${contextInfo.imports.slice(0, 10).join(', ')}\n`;
        }
        
        if (contextInfo.exports.length > 0) {
            context += `导出内容: ${contextInfo.exports.slice(0, 10).join(', ')}\n`;
        }
        
        if (contextInfo.dependencies.length > 0) {
            context += `项目依赖: ${contextInfo.dependencies.slice(0, 10).join(', ')}\n`;
        }
        
        if (contextInfo.relatedFiles.length > 0) {
            context += `\n相关文件:\n`;
            for (const file of contextInfo.relatedFiles.slice(0, 5)) {
                context += `- ${path.basename(file.path)}\n`;
            }
        }
        
        return context;
    }

    /**
     * 解析导入路径
     */
    private async resolveImportPath(importPath: string, currentDir: string): Promise<string | null> {
        // 相对路径
        if (importPath.startsWith('./') || importPath.startsWith('../')) {
            const resolved = path.resolve(currentDir, importPath);
            const extensions = ['.js', '.ts', '.jsx', '.tsx', '.py', '.java', '.cs'];
            
            for (const ext of extensions) {
                const fullPath = resolved + ext;
                try {
                    await vscode.workspace.fs.stat(vscode.Uri.file(fullPath));
                    return fullPath;
                } catch {
                    continue;
                }
            }
        }
        
        return null;
    }

    /**
     * 判断是否为代码文件
     */
    private isCodeFile(fileName: string): boolean {
        const codeExtensions = ['.js', '.ts', '.jsx', '.tsx', '.py', '.java', '.cs', '.cpp', '.c', '.php', '.rb', '.go'];
        return codeExtensions.some(ext => fileName.endsWith(ext));
    }

    /**
     * 根据文件扩展名获取语言
     */
    private getLanguageFromExtension(fileName: string): string {
        const ext = path.extname(fileName).toLowerCase();
        const languageMap: { [key: string]: string } = {
            '.js': 'javascript',
            '.jsx': 'javascript',
            '.ts': 'typescript',
            '.tsx': 'typescript',
            '.py': 'python',
            '.java': 'java',
            '.cs': 'csharp',
            '.cpp': 'cpp',
            '.c': 'c',
            '.php': 'php',
            '.rb': 'ruby',
            '.go': 'go'
        };
        
        return languageMap[ext] || 'text';
    }

    /**
     * 文档变更事件处理
     */
    onDocumentChange(event: vscode.TextDocumentChangeEvent): void {
        // 清除缓存中的文件信息
        const filePath = event.document.uri.fsPath;
        this.fileCache.delete(filePath);
    }

    /**
     * 活动编辑器变更事件处理
     */
    onActiveEditorChange(editor: vscode.TextEditor): void {
        // 可以在这里预加载上下文信息
    }

    /**
     * 选择变更事件处理
     */
    onSelectionChange(event: vscode.TextEditorSelectionChangeEvent): void {
        // 可以在这里处理选择变更相关的逻辑
    }

    /**
     * 清理资源
     */
    dispose(): void {
        this.fileCache.clear();
    }
}
