import * as vscode from 'vscode';
import { TestAssistantService } from './test-assistant-service';
import { HistoryManager } from './history-manager';

/**
 * 右键菜单操作类型
 */
export enum ContextMenuAction {
    ANALYZE_SELECTION = 'analyzeSelection',
    GENERATE_TEST = 'generateTest',
    FIX_ERROR = 'fixError',
    EXPLAIN_CODE = 'explainCode',
    OPTIMIZE_CODE = 'optimizeCode',
    ADD_COMMENTS = 'addComments',
    EXTRACT_METHOD = 'extractMethod',
    GENERATE_DOCS = 'generateDocs'
}

/**
 * 右键菜单提供者类
 */
export class ContextMenuProvider {
    private testAssistantService: TestAssistantService;
    private historyManager: HistoryManager;

    constructor(testAssistantService: TestAssistantService, historyManager: HistoryManager) {
        this.testAssistantService = testAssistantService;
        this.historyManager = historyManager;
    }

    /**
     * 注册右键菜单命令
     */
    registerContextMenuCommands(context: vscode.ExtensionContext): void {
        // 分析选中代码
        const analyzeSelectionCommand = vscode.commands.registerCommand(
            'testAssistant.analyzeSelection',
            () => this.handleContextMenuAction(ContextMenuAction.ANALYZE_SELECTION)
        );

        // 为选中代码生成测试
        const generateTestForSelectionCommand = vscode.commands.registerCommand(
            'testAssistant.generateTestForSelection',
            () => this.handleContextMenuAction(ContextMenuAction.GENERATE_TEST)
        );

        // 修复选中代码的错误
        const fixSelectionErrorCommand = vscode.commands.registerCommand(
            'testAssistant.fixSelectionError',
            () => this.handleContextMenuAction(ContextMenuAction.FIX_ERROR)
        );

        // 解释选中代码
        const explainSelectionCommand = vscode.commands.registerCommand(
            'testAssistant.explainSelection',
            () => this.handleContextMenuAction(ContextMenuAction.EXPLAIN_CODE)
        );

        // 优化选中代码
        const optimizeSelectionCommand = vscode.commands.registerCommand(
            'testAssistant.optimizeSelection',
            () => this.handleContextMenuAction(ContextMenuAction.OPTIMIZE_CODE)
        );

        // 为选中代码添加注释
        const addCommentsCommand = vscode.commands.registerCommand(
            'testAssistant.addComments',
            () => this.handleContextMenuAction(ContextMenuAction.ADD_COMMENTS)
        );

        // 提取方法
        const extractMethodCommand = vscode.commands.registerCommand(
            'testAssistant.extractMethod',
            () => this.handleContextMenuAction(ContextMenuAction.EXTRACT_METHOD)
        );

        // 生成文档
        const generateDocsCommand = vscode.commands.registerCommand(
            'testAssistant.generateDocs',
            () => this.handleContextMenuAction(ContextMenuAction.GENERATE_DOCS)
        );

        context.subscriptions.push(
            analyzeSelectionCommand,
            generateTestForSelectionCommand,
            fixSelectionErrorCommand,
            explainSelectionCommand,
            optimizeSelectionCommand,
            addCommentsCommand,
            extractMethodCommand,
            generateDocsCommand
        );
    }

    /**
     * 处理右键菜单操作
     */
    private async handleContextMenuAction(action: ContextMenuAction): Promise<void> {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showWarningMessage('请先打开一个代码文件');
            return;
        }

        const selection = editor.selection;
        if (selection.isEmpty) {
            vscode.window.showWarningMessage('请先选择要处理的代码');
            return;
        }

        const selectedText = editor.document.getText(selection);
        const language = editor.document.languageId;
        const fileName = editor.document.fileName;

        try {
            switch (action) {
                case ContextMenuAction.ANALYZE_SELECTION:
                    await this.analyzeSelection(selectedText, language, fileName);
                    break;
                case ContextMenuAction.GENERATE_TEST:
                    await this.generateTestForSelection(selectedText, language, fileName);
                    break;
                case ContextMenuAction.FIX_ERROR:
                    await this.fixSelectionError(selectedText, language, fileName);
                    break;
                case ContextMenuAction.EXPLAIN_CODE:
                    await this.explainSelection(selectedText, language, fileName);
                    break;
                case ContextMenuAction.OPTIMIZE_CODE:
                    await this.optimizeSelection(selectedText, language, fileName);
                    break;
                case ContextMenuAction.ADD_COMMENTS:
                    await this.addCommentsToSelection(selectedText, language, fileName, editor, selection);
                    break;
                case ContextMenuAction.EXTRACT_METHOD:
                    await this.extractMethodFromSelection(selectedText, language, fileName, editor, selection);
                    break;
                case ContextMenuAction.GENERATE_DOCS:
                    await this.generateDocsForSelection(selectedText, language, fileName);
                    break;
            }
        } catch (error) {
            vscode.window.showErrorMessage(`操作失败: ${error}`);
        }
    }

    /**
     * 分析选中代码
     */
    private async analyzeSelection(code: string, language: string, fileName: string): Promise<void> {
        await vscode.window.withProgress({
            location: vscode.ProgressLocation.Notification,
            title: "正在分析选中代码...",
            cancellable: false
        }, async (progress) => {
            progress.report({ increment: 0 });

            const result = await this.testAssistantService.analyzeCode(code, language);
            
            progress.report({ increment: 70 });

            if (result.success) {
                await this.showResultInNewDocument(
                    `# 代码分析结果\n\n**文件:** ${fileName}\n\n**分析结果:**\n${result.analysis}\n\n${result.suggestions ? '**建议:**\n' + result.suggestions.join('\n') : ''}`,
                    'markdown'
                );

                this.historyManager.addAnalysisHistory({
                    code,
                    language,
                    result,
                    timestamp: new Date()
                });
            } else {
                vscode.window.showErrorMessage(`分析失败: ${result.error}`);
            }

            progress.report({ increment: 100 });
        });
    }

    /**
     * 为选中代码生成测试
     */
    private async generateTestForSelection(code: string, language: string, fileName: string): Promise<void> {
        const testType = await vscode.window.showQuickPick([
            { label: '单元测试', value: 'unit' },
            { label: '集成测试', value: 'integration' },
            { label: '边界测试', value: 'boundary' },
            { label: '异常测试', value: 'exception' }
        ], {
            placeHolder: '选择测试类型'
        });

        if (!testType) return;

        await vscode.window.withProgress({
            location: vscode.ProgressLocation.Notification,
            title: "正在生成测试代码...",
            cancellable: false
        }, async (progress) => {
            progress.report({ increment: 0 });

            const result = await this.testAssistantService.generateTestSuggestions(code, language, testType.value);
            
            progress.report({ increment: 70 });

            if (result.success) {
                let content = `# 测试代码生成结果\n\n**文件:** ${fileName}\n**测试类型:** ${testType.label}\n\n`;
                
                if (result.test_cases && result.test_cases.length > 0) {
                    content += '**测试用例:**\n' + result.test_cases.join('\n') + '\n\n';
                }
                
                if (result.test_code) {
                    content += `**测试代码:**\n\`\`\`${language}\n${result.test_code}\n\`\`\`\n\n`;
                }

                await this.showResultInNewDocument(content, 'markdown');

                this.historyManager.addTestHistory({
                    code,
                    language,
                    testType: testType.value,
                    result,
                    timestamp: new Date()
                });
            } else {
                vscode.window.showErrorMessage(`测试生成失败: ${result.error}`);
            }

            progress.report({ increment: 100 });
        });
    }

    /**
     * 修复选中代码的错误
     */
    private async fixSelectionError(code: string, language: string, fileName: string): Promise<void> {
        const errorMessage = await vscode.window.showInputBox({
            prompt: '请描述遇到的错误或问题',
            placeHolder: '例如：编译错误、运行时异常、逻辑错误等'
        });

        if (!errorMessage) return;

        await vscode.window.withProgress({
            location: vscode.ProgressLocation.Notification,
            title: "正在分析错误并生成修复建议...",
            cancellable: false
        }, async (progress) => {
            progress.report({ increment: 0 });

            const result = await this.testAssistantService.fixError(code, errorMessage, language);
            
            progress.report({ increment: 70 });

            if (result.success) {
                let content = `# 错误修复建议\n\n**文件:** ${fileName}\n**错误描述:** ${errorMessage}\n\n`;
                
                if (result.analysis) {
                    content += `**错误分析:**\n${result.analysis}\n\n`;
                }
                
                if (result.fix_suggestions && result.fix_suggestions.length > 0) {
                    content += '**修复建议:**\n' + result.fix_suggestions.join('\n') + '\n\n';
                }
                
                if (result.fixed_code) {
                    content += `**修复后的代码:**\n\`\`\`${language}\n${result.fixed_code}\n\`\`\``;
                }

                await this.showResultInNewDocument(content, 'markdown');
            } else {
                vscode.window.showErrorMessage(`错误修复失败: ${result.error}`);
            }

            progress.report({ increment: 100 });
        });
    }

    /**
     * 解释选中代码
     */
    private async explainSelection(code: string, language: string, fileName: string): Promise<void> {
        await vscode.window.withProgress({
            location: vscode.ProgressLocation.Notification,
            title: "正在解释代码...",
            cancellable: false
        }, async (progress) => {
            progress.report({ increment: 0 });

            const prompt = `请详细解释以下${language}代码的功能、逻辑和实现原理：\n\n\`\`\`${language}\n${code}\n\`\`\``;
            const result = await this.testAssistantService.analyzeCode(code, language, prompt);
            
            progress.report({ increment: 70 });

            if (result.success) {
                await this.showResultInNewDocument(
                    `# 代码解释\n\n**文件:** ${fileName}\n\n**代码:**\n\`\`\`${language}\n${code}\n\`\`\`\n\n**解释:**\n${result.analysis}`,
                    'markdown'
                );
            } else {
                vscode.window.showErrorMessage(`代码解释失败: ${result.error}`);
            }

            progress.report({ increment: 100 });
        });
    }

    /**
     * 优化选中代码
     */
    private async optimizeSelection(code: string, language: string, fileName: string): Promise<void> {
        await vscode.window.withProgress({
            location: vscode.ProgressLocation.Notification,
            title: "正在优化代码...",
            cancellable: false
        }, async (progress) => {
            progress.report({ increment: 0 });

            const prompt = `请分析以下${language}代码并提供优化建议，包括性能优化、代码简化、最佳实践等：\n\n\`\`\`${language}\n${code}\n\`\`\``;
            const result = await this.testAssistantService.analyzeCode(code, language, prompt);
            
            progress.report({ increment: 70 });

            if (result.success) {
                await this.showResultInNewDocument(
                    `# 代码优化建议\n\n**文件:** ${fileName}\n\n**原始代码:**\n\`\`\`${language}\n${code}\n\`\`\`\n\n**优化建议:**\n${result.analysis}`,
                    'markdown'
                );
            } else {
                vscode.window.showErrorMessage(`代码优化失败: ${result.error}`);
            }

            progress.report({ increment: 100 });
        });
    }

    /**
     * 为选中代码添加注释
     */
    private async addCommentsToSelection(
        code: string, 
        language: string, 
        fileName: string, 
        editor: vscode.TextEditor, 
        selection: vscode.Selection
    ): Promise<void> {
        await vscode.window.withProgress({
            location: vscode.ProgressLocation.Notification,
            title: "正在生成注释...",
            cancellable: false
        }, async (progress) => {
            progress.report({ increment: 0 });

            const prompt = `请为以下${language}代码添加详细的注释，包括功能说明、参数说明、返回值说明等：\n\n\`\`\`${language}\n${code}\n\`\`\``;
            const result = await this.testAssistantService.analyzeCode(code, language, prompt);
            
            progress.report({ increment: 70 });

            if (result.success && result.analysis) {
                // 尝试提取带注释的代码
                const codeBlockMatch = result.analysis.match(/```[\w]*\n([\s\S]*?)\n```/);
                if (codeBlockMatch) {
                    const commentedCode = codeBlockMatch[1];
                    
                    // 询问是否替换原代码
                    const action = await vscode.window.showInformationMessage(
                        '是否要用带注释的代码替换选中的代码？',
                        '替换', '查看', '取消'
                    );

                    if (action === '替换') {
                        await editor.edit(editBuilder => {
                            editBuilder.replace(selection, commentedCode);
                        });
                    } else if (action === '查看') {
                        await this.showResultInNewDocument(
                            `# 带注释的代码\n\n**文件:** ${fileName}\n\n\`\`\`${language}\n${commentedCode}\n\`\`\``,
                            'markdown'
                        );
                    }
                } else {
                    await this.showResultInNewDocument(
                        `# 注释建议\n\n**文件:** ${fileName}\n\n${result.analysis}`,
                        'markdown'
                    );
                }
            } else {
                vscode.window.showErrorMessage(`注释生成失败: ${result.error}`);
            }

            progress.report({ increment: 100 });
        });
    }

    /**
     * 从选中代码提取方法
     */
    private async extractMethodFromSelection(
        code: string, 
        language: string, 
        fileName: string, 
        editor: vscode.TextEditor, 
        selection: vscode.Selection
    ): Promise<void> {
        const methodName = await vscode.window.showInputBox({
            prompt: '请输入要提取的方法名称',
            placeHolder: '例如：calculateTotal'
        });

        if (!methodName) return;

        await vscode.window.withProgress({
            location: vscode.ProgressLocation.Notification,
            title: "正在提取方法...",
            cancellable: false
        }, async (progress) => {
            progress.report({ increment: 0 });

            const prompt = `请将以下${language}代码重构为一个名为"${methodName}"的方法，并提供调用示例：\n\n\`\`\`${language}\n${code}\n\`\`\``;
            const result = await this.testAssistantService.analyzeCode(code, language, prompt);
            
            progress.report({ increment: 70 });

            if (result.success) {
                await this.showResultInNewDocument(
                    `# 方法提取结果\n\n**文件:** ${fileName}\n**方法名:** ${methodName}\n\n**重构建议:**\n${result.analysis}`,
                    'markdown'
                );
            } else {
                vscode.window.showErrorMessage(`方法提取失败: ${result.error}`);
            }

            progress.report({ increment: 100 });
        });
    }

    /**
     * 为选中代码生成文档
     */
    private async generateDocsForSelection(code: string, language: string, fileName: string): Promise<void> {
        await vscode.window.withProgress({
            location: vscode.ProgressLocation.Notification,
            title: "正在生成文档...",
            cancellable: false
        }, async (progress) => {
            progress.report({ increment: 0 });

            const prompt = `请为以下${language}代码生成详细的技术文档，包括功能描述、使用方法、参数说明、示例等：\n\n\`\`\`${language}\n${code}\n\`\`\``;
            const result = await this.testAssistantService.analyzeCode(code, language, prompt);
            
            progress.report({ increment: 70 });

            if (result.success) {
                await this.showResultInNewDocument(
                    `# 代码文档\n\n**文件:** ${fileName}\n\n**代码:**\n\`\`\`${language}\n${code}\n\`\`\`\n\n**文档:**\n${result.analysis}`,
                    'markdown'
                );
            } else {
                vscode.window.showErrorMessage(`文档生成失败: ${result.error}`);
            }

            progress.report({ increment: 100 });
        });
    }

    /**
     * 在新文档中显示结果
     */
    private async showResultInNewDocument(content: string, language: string = 'markdown'): Promise<void> {
        const document = await vscode.workspace.openTextDocument({
            content: content,
            language: language
        });
        await vscode.window.showTextDocument(document);
    }
}
