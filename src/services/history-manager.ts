import * as vscode from 'vscode';

/**
 * 历史记录类型枚举
 */
export enum HistoryType {
    ANALYSIS = 'analysis',
    TEST = 'test',
    ERROR_FIX = 'error_fix',
    INTERFACE_TEST = 'interface_test',
    TEST_DATA = 'test_data',
    CHAT = 'chat'
}

/**
 * 基础历史记录接口
 */
export interface BaseHistoryItem {
    id: string;
    type: HistoryType;
    timestamp: Date;
    code?: string;
    language?: string;
    result?: any;
}

/**
 * 分析历史记录接口
 */
export interface AnalysisHistoryItem extends BaseHistoryItem {
    type: HistoryType.ANALYSIS;
    result: {
        success: boolean;
        analysis?: string;
        suggestions?: string[];
        error?: string;
    };
}

/**
 * 测试历史记录接口
 */
export interface TestHistoryItem extends BaseHistoryItem {
    type: HistoryType.TEST;
    testType: string;
    result: {
        success: boolean;
        test_cases?: string[];
        test_code?: string;
        suggestions?: string[];
        error?: string;
    };
}

/**
 * 对话历史记录接口
 */
export interface ChatHistoryItem extends BaseHistoryItem {
    type: HistoryType.CHAT;
    userMessage: string;
    assistantResponse: string;
    context?: string;
}

/**
 * 会话状态接口
 */
export interface SessionState {
    sessionId: string;
    startTime: Date;
    lastActivity: Date;
    messageCount: number;
    context: Map<string, any>;
    isActive: boolean;
}

/**
 * 历史管理器类
 */
export class HistoryManager {
    private context: vscode.ExtensionContext;
    private maxHistoryItems: number;
    private currentSession: SessionState;
    private analysisHistory: AnalysisHistoryItem[] = [];
    private testHistory: TestHistoryItem[] = [];
    private chatHistory: ChatHistoryItem[] = [];
    private sessionHistory: SessionState[] = [];

    constructor(context: vscode.ExtensionContext) {
        this.context = context;
        this.maxHistoryItems = vscode.workspace.getConfiguration('testAssistant.chat').get('maxHistory', 50);
        this.currentSession = this.createNewSession();
        this.loadHistoryFromStorage();
    }

    /**
     * 创建新会话
     */
    private createNewSession(): SessionState {
        return {
            sessionId: this.generateId(),
            startTime: new Date(),
            lastActivity: new Date(),
            messageCount: 0,
            context: new Map(),
            isActive: true
        };
    }

    /**
     * 添加分析历史记录
     */
    addAnalysisHistory(item: Omit<AnalysisHistoryItem, 'id' | 'type'>): void {
        const historyItem: AnalysisHistoryItem = {
            id: this.generateId(),
            type: HistoryType.ANALYSIS,
            ...item
        };

        this.analysisHistory.unshift(historyItem);
        this.trimHistory(this.analysisHistory);
        this.updateSessionActivity();
        this.saveHistoryToStorage();
    }

    /**
     * 添加测试历史记录
     */
    addTestHistory(item: Omit<TestHistoryItem, 'id' | 'type'>): void {
        const historyItem: TestHistoryItem = {
            id: this.generateId(),
            type: HistoryType.TEST,
            ...item
        };

        this.testHistory.unshift(historyItem);
        this.trimHistory(this.testHistory);
        this.updateSessionActivity();
        this.saveHistoryToStorage();
    }

    /**
     * 添加对话历史记录
     */
    addChatHistory(userMessage: string, assistantResponse: string, context?: string): void {
        const historyItem: ChatHistoryItem = {
            id: this.generateId(),
            type: HistoryType.CHAT,
            timestamp: new Date(),
            userMessage,
            assistantResponse,
            context
        };

        this.chatHistory.unshift(historyItem);
        this.trimHistory(this.chatHistory);
        this.updateSessionActivity();
        this.currentSession.messageCount++;
        this.saveHistoryToStorage();
    }

    /**
     * 获取分析历史记录
     */
    getAnalysisHistory(limit?: number): AnalysisHistoryItem[] {
        return limit ? this.analysisHistory.slice(0, limit) : this.analysisHistory;
    }

    /**
     * 获取测试历史记录
     */
    getTestHistory(limit?: number): TestHistoryItem[] {
        return limit ? this.testHistory.slice(0, limit) : this.testHistory;
    }

    /**
     * 获取对话历史记录
     */
    getChatHistory(limit?: number): ChatHistoryItem[] {
        return limit ? this.chatHistory.slice(0, limit) : this.chatHistory;
    }

    /**
     * 获取最近的对话上下文
     */
    getRecentChatContext(messageCount: number = 5): string {
        const recentChats = this.chatHistory.slice(0, messageCount);
        let context = '';

        for (const chat of recentChats.reverse()) {
            context += `用户: ${chat.userMessage}\n`;
            context += `助手: ${chat.assistantResponse}\n\n`;
        }

        return context;
    }

    /**
     * 搜索历史记录
     */
    searchHistory(query: string, type?: HistoryType): BaseHistoryItem[] {
        const results: BaseHistoryItem[] = [];
        const lowerQuery = query.toLowerCase();

        const searchInArray = (items: BaseHistoryItem[]) => {
            return items.filter(item => {
                if (type && item.type !== type) {
                    return false;
                }

                // 搜索代码内容
                if (item.code && item.code.toLowerCase().includes(lowerQuery)) {
                    return true;
                }

                // 搜索结果内容
                if (item.result) {
                    const resultStr = JSON.stringify(item.result).toLowerCase();
                    if (resultStr.includes(lowerQuery)) {
                        return true;
                    }
                }

                // 搜索对话内容
                if (item.type === HistoryType.CHAT) {
                    const chatItem = item as ChatHistoryItem;
                    if (chatItem.userMessage.toLowerCase().includes(lowerQuery) ||
                        chatItem.assistantResponse.toLowerCase().includes(lowerQuery)) {
                        return true;
                    }
                }

                return false;
            });
        };

        results.push(...searchInArray(this.analysisHistory));
        results.push(...searchInArray(this.testHistory));
        results.push(...searchInArray(this.chatHistory));

        // 按时间排序
        return results.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
    }

    /**
     * 清除历史记录
     */
    clearHistory(type?: HistoryType): void {
        if (!type) {
            this.analysisHistory = [];
            this.testHistory = [];
            this.chatHistory = [];
        } else {
            switch (type) {
                case HistoryType.ANALYSIS:
                    this.analysisHistory = [];
                    break;
                case HistoryType.TEST:
                    this.testHistory = [];
                    break;
                case HistoryType.CHAT:
                    this.chatHistory = [];
                    break;
            }
        }

        this.saveHistoryToStorage();
    }

    /**
     * 导出历史记录
     */
    exportHistory(type?: HistoryType): string {
        let content = '# 测试助手历史记录\n\n';
        content += `导出时间: ${new Date().toLocaleString()}\n\n`;

        if (!type || type === HistoryType.ANALYSIS) {
            content += '## 代码分析历史\n\n';
            for (const item of this.analysisHistory) {
                content += `### ${item.timestamp.toLocaleString()}\n`;
                content += `语言: ${item.language}\n`;
                if (item.result.success) {
                    content += `分析结果: ${item.result.analysis}\n`;
                    if (item.result.suggestions) {
                        content += `建议: ${item.result.suggestions.join(', ')}\n`;
                    }
                } else {
                    content += `错误: ${item.result.error}\n`;
                }
                content += '\n---\n\n';
            }
        }

        if (!type || type === HistoryType.TEST) {
            content += '## 测试建议历史\n\n';
            for (const item of this.testHistory) {
                content += `### ${item.timestamp.toLocaleString()}\n`;
                content += `语言: ${item.language}\n`;
                content += `测试类型: ${item.testType}\n`;
                if (item.result.success) {
                    if (item.result.test_cases) {
                        content += `测试用例: ${item.result.test_cases.join(', ')}\n`;
                    }
                    if (item.result.test_code) {
                        content += `测试代码:\n\`\`\`\n${item.result.test_code}\n\`\`\`\n`;
                    }
                } else {
                    content += `错误: ${item.result.error}\n`;
                }
                content += '\n---\n\n';
            }
        }

        if (!type || type === HistoryType.CHAT) {
            content += '## 对话历史\n\n';
            for (const item of this.chatHistory) {
                content += `### ${item.timestamp.toLocaleString()}\n`;
                content += `用户: ${item.userMessage}\n`;
                content += `助手: ${item.assistantResponse}\n`;
                if (item.context) {
                    content += `上下文: ${item.context}\n`;
                }
                content += '\n---\n\n';
            }
        }

        return content;
    }

    /**
     * 获取当前会话状态
     */
    getCurrentSession(): SessionState {
        return { ...this.currentSession };
    }

    /**
     * 开始新会话
     */
    startNewSession(): void {
        // 保存当前会话
        this.currentSession.isActive = false;
        this.sessionHistory.unshift({ ...this.currentSession });
        this.trimHistory(this.sessionHistory, 10); // 只保留最近10个会话

        // 创建新会话
        this.currentSession = this.createNewSession();
        this.saveHistoryToStorage();
    }

    /**
     * 设置会话上下文
     */
    setSessionContext(key: string, value: any): void {
        this.currentSession.context.set(key, value);
        this.updateSessionActivity();
    }

    /**
     * 获取会话上下文
     */
    getSessionContext(key: string): any {
        return this.currentSession.context.get(key);
    }

    /**
     * 更新会话活动时间
     */
    private updateSessionActivity(): void {
        this.currentSession.lastActivity = new Date();
    }

    /**
     * 修剪历史记录
     */
    private trimHistory(history: any[], maxItems?: number): void {
        const limit = maxItems || this.maxHistoryItems;
        if (history.length > limit) {
            history.splice(limit);
        }
    }

    /**
     * 生成唯一ID
     */
    private generateId(): string {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    /**
     * 从存储加载历史记录
     */
    private loadHistoryFromStorage(): void {
        try {
            const analysisData = this.context.globalState.get<AnalysisHistoryItem[]>('analysisHistory', []);
            const testData = this.context.globalState.get<TestHistoryItem[]>('testHistory', []);
            const chatData = this.context.globalState.get<ChatHistoryItem[]>('chatHistory', []);
            const sessionData = this.context.globalState.get<SessionState[]>('sessionHistory', []);

            // 恢复日期对象
            this.analysisHistory = analysisData.map(item => ({
                ...item,
                timestamp: new Date(item.timestamp)
            }));

            this.testHistory = testData.map(item => ({
                ...item,
                timestamp: new Date(item.timestamp)
            }));

            this.chatHistory = chatData.map(item => ({
                ...item,
                timestamp: new Date(item.timestamp)
            }));

            this.sessionHistory = sessionData.map(session => ({
                ...session,
                startTime: new Date(session.startTime),
                lastActivity: new Date(session.lastActivity),
                context: new Map(Object.entries(session.context || {}))
            }));

        } catch (error) {
            console.warn('加载历史记录失败:', error);
        }
    }

    /**
     * 保存历史记录到存储
     */
    private saveHistoryToStorage(): void {
        try {
            this.context.globalState.update('analysisHistory', this.analysisHistory);
            this.context.globalState.update('testHistory', this.testHistory);
            this.context.globalState.update('chatHistory', this.chatHistory);
            
            // 转换Map为对象以便序列化
            const sessionDataForStorage = this.sessionHistory.map(session => ({
                ...session,
                context: Object.fromEntries(session.context)
            }));
            this.context.globalState.update('sessionHistory', sessionDataForStorage);

        } catch (error) {
            console.warn('保存历史记录失败:', error);
        }
    }

    /**
     * 获取统计信息
     */
    getStatistics(): {
        totalAnalysis: number;
        totalTests: number;
        totalChats: number;
        currentSessionMessages: number;
        sessionDuration: number;
    } {
        const sessionDuration = Date.now() - this.currentSession.startTime.getTime();
        
        return {
            totalAnalysis: this.analysisHistory.length,
            totalTests: this.testHistory.length,
            totalChats: this.chatHistory.length,
            currentSessionMessages: this.currentSession.messageCount,
            sessionDuration: Math.floor(sessionDuration / 1000) // 秒
        };
    }

    /**
     * 清理资源
     */
    dispose(): void {
        this.saveHistoryToStorage();
    }
}
