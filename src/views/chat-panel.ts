import * as vscode from 'vscode';
import { TestAssistantService } from '../services/test-assistant-service';

/**
 * 消息类型枚举
 */
export enum MessageType {
    USER = 'user',
    ASSISTANT = 'assistant',
    SYSTEM = 'system',
    ERROR = 'error'
}

/**
 * 消息接口
 */
export interface ChatMessage {
    id: string;
    type: MessageType;
    content: string;
    timestamp: Date;
    metadata?: any;
}

/**
 * 对话面板类
 */
export class ChatPanel {
    public static currentPanel: ChatPanel | undefined;
    private readonly _panel: vscode.WebviewPanel;
    private readonly _extensionUri: vscode.Uri;
    private _disposables: vscode.Disposable[] = [];
    private _messages: ChatMessage[] = [];
    private _testAssistantService: TestAssistantService;

    public static createOrShow(extensionUri: vscode.Uri, testAssistantService: TestAssistantService) {
        const column = vscode.window.activeTextEditor
            ? vscode.window.activeTextEditor.viewColumn
            : undefined;

        // 如果已经有面板，则显示它
        if (ChatPanel.currentPanel) {
            ChatPanel.currentPanel._panel.reveal(column);
            return;
        }

        // 否则，创建新面板
        const panel = vscode.window.createWebviewPanel(
            'testAssistantChat',
            '测试助手对话',
            column || vscode.ViewColumn.One,
            {
                enableScripts: true,
                retainContextWhenHidden: true,
                localResourceRoots: [
                    vscode.Uri.joinPath(extensionUri, 'media'),
                    vscode.Uri.joinPath(extensionUri, 'out')
                ]
            }
        );

        ChatPanel.currentPanel = new ChatPanel(panel, extensionUri, testAssistantService);
    }

    private constructor(panel: vscode.WebviewPanel, extensionUri: vscode.Uri, testAssistantService: TestAssistantService) {
        this._panel = panel;
        this._extensionUri = extensionUri;
        this._testAssistantService = testAssistantService;

        // 设置初始HTML内容
        this._update();

        // 监听面板被关闭
        this._panel.onDidDispose(() => this.dispose(), null, this._disposables);

        // 处理来自webview的消息
        this._panel.webview.onDidReceiveMessage(
            async (message) => {
                await this._handleMessage(message);
            },
            null,
            this._disposables
        );
    }

    /**
     * 处理来自webview的消息
     */
    private async _handleMessage(message: any) {
        switch (message.command) {
            case 'sendMessage':
                await this._handleUserMessage(message.text);
                break;
            case 'analyzeCurrentCode':
                await this._analyzeCurrentCode();
                break;
            case 'generateTest':
                await this._generateTestForCurrentCode();
                break;
            case 'clearHistory':
                this._clearHistory();
                break;
            case 'exportHistory':
                this._exportHistory();
                break;
        }
    }

    /**
     * 处理用户消息
     */
    private async _handleUserMessage(text: string) {
        if (!text.trim()) {
            return;
        }

        // 添加用户消息
        const userMessage: ChatMessage = {
            id: this._generateId(),
            type: MessageType.USER,
            content: text,
            timestamp: new Date()
        };
        this._addMessage(userMessage);

        // 显示正在思考状态
        this._showThinking();

        try {
            // 分析消息内容，确定处理方式
            const response = await this._processUserMessage(text);
            
            // 添加助手回复
            const assistantMessage: ChatMessage = {
                id: this._generateId(),
                type: MessageType.ASSISTANT,
                content: response,
                timestamp: new Date()
            };
            this._addMessage(assistantMessage);

        } catch (error) {
            // 添加错误消息
            const errorMessage: ChatMessage = {
                id: this._generateId(),
                type: MessageType.ERROR,
                content: `处理消息时发生错误: ${error}`,
                timestamp: new Date()
            };
            this._addMessage(errorMessage);
        } finally {
            this._hideThinking();
        }
    }

    /**
     * 处理用户消息内容
     */
    private async _processUserMessage(text: string): Promise<string> {
        const lowerText = text.toLowerCase();
        
        // 获取当前编辑器信息
        const editorInfo = this._testAssistantService.getCurrentEditorInfo();
        
        if (lowerText.includes('分析') || lowerText.includes('理解')) {
            if (editorInfo) {
                const result = await this._testAssistantService.analyzeCode(
                    editorInfo.code, 
                    editorInfo.language
                );
                return result.success ? result.analysis || '分析完成' : result.error || '分析失败';
            } else {
                return '请先打开一个代码文件';
            }
        }
        
        if (lowerText.includes('测试') || lowerText.includes('test')) {
            if (editorInfo) {
                const result = await this._testAssistantService.generateTestSuggestions(
                    editorInfo.code, 
                    editorInfo.language
                );
                if (result.success) {
                    let response = '测试建议：\n\n';
                    if (result.test_cases && result.test_cases.length > 0) {
                        response += '测试用例：\n' + result.test_cases.join('\n') + '\n\n';
                    }
                    if (result.test_code) {
                        response += '测试代码：\n```\n' + result.test_code + '\n```';
                    }
                    return response;
                } else {
                    return result.error || '测试建议生成失败';
                }
            } else {
                return '请先打开一个代码文件';
            }
        }
        
        // 默认处理：直接发送给模型
        if (editorInfo) {
            const result = await this._testAssistantService.analyzeCode(
                editorInfo.code, 
                editorInfo.language, 
                `用户问题: ${text}`
            );
            return result.success ? result.analysis || '处理完成' : result.error || '处理失败';
        } else {
            return '您好！我是测试助手，可以帮助您分析代码、生成测试建议、修复错误等。请打开一个代码文件开始使用。';
        }
    }

    /**
     * 分析当前代码
     */
    private async _analyzeCurrentCode() {
        const editorInfo = this._testAssistantService.getCurrentEditorInfo();
        if (!editorInfo) {
            this._showError('请先打开一个代码文件');
            return;
        }

        this._showThinking();
        
        try {
            const result = await this._testAssistantService.analyzeCode(
                editorInfo.code, 
                editorInfo.language
            );
            
            if (result.success) {
                const message: ChatMessage = {
                    id: this._generateId(),
                    type: MessageType.ASSISTANT,
                    content: `代码分析结果：\n\n${result.analysis}`,
                    timestamp: new Date()
                };
                this._addMessage(message);
            } else {
                this._showError(result.error || '代码分析失败');
            }
        } catch (error) {
            this._showError(`分析过程中发生错误: ${error}`);
        } finally {
            this._hideThinking();
        }
    }

    /**
     * 为当前代码生成测试
     */
    private async _generateTestForCurrentCode() {
        const editorInfo = this._testAssistantService.getCurrentEditorInfo();
        if (!editorInfo) {
            this._showError('请先打开一个代码文件');
            return;
        }

        this._showThinking();
        
        try {
            const result = await this._testAssistantService.generateTestSuggestions(
                editorInfo.code, 
                editorInfo.language
            );
            
            if (result.success) {
                let content = '测试建议：\n\n';
                
                if (result.test_cases && result.test_cases.length > 0) {
                    content += '测试用例：\n' + result.test_cases.join('\n') + '\n\n';
                }
                
                if (result.test_code) {
                    content += '测试代码：\n```' + editorInfo.language + '\n' + result.test_code + '\n```';
                }
                
                const message: ChatMessage = {
                    id: this._generateId(),
                    type: MessageType.ASSISTANT,
                    content: content,
                    timestamp: new Date()
                };
                this._addMessage(message);
            } else {
                this._showError(result.error || '测试生成失败');
            }
        } catch (error) {
            this._showError(`生成测试过程中发生错误: ${error}`);
        } finally {
            this._hideThinking();
        }
    }

    /**
     * 添加消息
     */
    private _addMessage(message: ChatMessage) {
        this._messages.push(message);
        this._updateMessages();
    }

    /**
     * 显示错误消息
     */
    private _showError(error: string) {
        const errorMessage: ChatMessage = {
            id: this._generateId(),
            type: MessageType.ERROR,
            content: error,
            timestamp: new Date()
        };
        this._addMessage(errorMessage);
    }

    /**
     * 显示正在思考状态
     */
    private _showThinking() {
        this._panel.webview.postMessage({
            command: 'showThinking'
        });
    }

    /**
     * 隐藏正在思考状态
     */
    private _hideThinking() {
        this._panel.webview.postMessage({
            command: 'hideThinking'
        });
    }

    /**
     * 更新消息显示
     */
    private _updateMessages() {
        this._panel.webview.postMessage({
            command: 'updateMessages',
            messages: this._messages
        });
    }

    /**
     * 清除历史记录
     */
    private _clearHistory() {
        this._messages = [];
        this._updateMessages();
    }

    /**
     * 导出历史记录
     */
    private _exportHistory() {
        const content = this._messages.map(msg => 
            `[${msg.timestamp.toLocaleString()}] ${msg.type.toUpperCase()}: ${msg.content}`
        ).join('\n\n');
        
        vscode.workspace.openTextDocument({
            content: content,
            language: 'markdown'
        }).then(doc => {
            vscode.window.showTextDocument(doc);
        });
    }

    /**
     * 生成唯一ID
     */
    private _generateId(): string {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    /**
     * 更新webview内容
     */
    private _update() {
        this._panel.webview.html = this._getHtmlForWebview();
    }

    /**
     * 获取webview的HTML内容
     */
    private _getHtmlForWebview(): string {
        const scriptUri = this._panel.webview.asWebviewUri(
            vscode.Uri.joinPath(this._extensionUri, 'out', 'media', 'chat.js')
        );
        const styleUri = this._panel.webview.asWebviewUri(
            vscode.Uri.joinPath(this._extensionUri, 'out', 'media', 'chat.css')
        );

        return `<!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <link href="${styleUri}" rel="stylesheet">
            <title>测试助手对话</title>
        </head>
        <body>
            <div class="chat-container">
                <div class="chat-header">
                    <h3>测试助手</h3>
                    <div class="header-buttons">
                        <button id="analyzeBtn" class="header-btn">分析当前代码</button>
                        <button id="testBtn" class="header-btn">生成测试</button>
                        <button id="clearBtn" class="header-btn">清除历史</button>
                    </div>
                </div>
                
                <div class="chat-messages" id="chatMessages">
                    <div class="welcome-message">
                        <p>👋 您好！我是测试助手，可以帮助您：</p>
                        <ul>
                            <li>🔍 分析和理解代码</li>
                            <li>🧪 生成测试建议和用例</li>
                            <li>🔧 修复代码错误</li>
                            <li>🌐 接口测试建议</li>
                            <li>📊 准备测试数据</li>
                        </ul>
                        <p>请打开代码文件开始使用，或直接在下方输入您的问题。</p>
                    </div>
                </div>
                
                <div class="thinking-indicator" id="thinkingIndicator" style="display: none;">
                    <div class="thinking-dots">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                    <span>正在思考...</span>
                </div>
                
                <div class="chat-input">
                    <div class="input-container">
                        <textarea id="messageInput" placeholder="输入您的问题或选择上方快捷操作..." rows="3"></textarea>
                        <button id="sendBtn" class="send-btn">发送</button>
                    </div>
                </div>
            </div>
            
            <script src="${scriptUri}"></script>
        </body>
        </html>`;
    }

    public dispose() {
        ChatPanel.currentPanel = undefined;

        // 清理面板
        this._panel.dispose();

        // 清理所有disposables
        while (this._disposables.length) {
            const x = this._disposables.pop();
            if (x) {
                x.dispose();
            }
        }
    }
}
