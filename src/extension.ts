import * as vscode from 'vscode';
import { TestAssistantService } from './services/test-assistant-service';
import { ChatPanel } from './views/chat-panel';
import { ContextAnalyzer } from './services/context-analyzer';
import { HistoryManager } from './services/history-manager';

let testAssistantService: TestAssistantService;
let contextAnalyzer: ContextAnalyzer;
let historyManager: HistoryManager;

/**
 * 插件激活函数
 */
export async function activate(context: vscode.ExtensionContext) {
    console.log('测试助手插件正在激活...');

    // 初始化服务
    testAssistantService = new TestAssistantService(context);
    contextAnalyzer = new ContextAnalyzer(context);
    historyManager = new HistoryManager(context);

    // 初始化服务
    const initialized = await testAssistantService.initialize();
    if (!initialized) {
        vscode.window.showWarningMessage('测试助手初始化失败，请检查企业模型配置');
    }

    // 注册命令
    registerCommands(context);

    // 注册事件监听器
    registerEventListeners(context);

    // 显示激活消息
    vscode.window.showInformationMessage('测试助手已激活！');
    
    console.log('测试助手插件激活完成');
}

/**
 * 注册命令
 */
function registerCommands(context: vscode.ExtensionContext) {
    // 打开对话面板
    const openChatCommand = vscode.commands.registerCommand('testAssistant.openChat', () => {
        ChatPanel.createOrShow(context.extensionUri, testAssistantService);
    });

    // 分析代码
    const analyzeCodeCommand = vscode.commands.registerCommand('testAssistant.analyzeCode', async () => {
        await analyzeCurrentCode();
    });

    // 生成测试建议
    const generateTestCommand = vscode.commands.registerCommand('testAssistant.generateTest', async () => {
        await generateTestSuggestions();
    });

    // 修复错误
    const fixErrorCommand = vscode.commands.registerCommand('testAssistant.fixError', async () => {
        await fixError();
    });

    // 接口测试
    const testInterfaceCommand = vscode.commands.registerCommand('testAssistant.testInterface', async () => {
        await testInterface();
    });

    // 准备测试数据
    const prepareTestDataCommand = vscode.commands.registerCommand('testAssistant.prepareTestData', async () => {
        await prepareTestData();
    });

    // 打开设置
    const openSettingsCommand = vscode.commands.registerCommand('testAssistant.openSettings', () => {
        vscode.commands.executeCommand('workbench.action.openSettings', 'testAssistant');
    });

    // 注册所有命令
    context.subscriptions.push(
        openChatCommand,
        analyzeCodeCommand,
        generateTestCommand,
        fixErrorCommand,
        testInterfaceCommand,
        prepareTestDataCommand,
        openSettingsCommand
    );
}

/**
 * 注册事件监听器
 */
function registerEventListeners(context: vscode.ExtensionContext) {
    // 监听配置变更
    const configChangeListener = vscode.workspace.onDidChangeConfiguration(event => {
        if (event.affectsConfiguration('testAssistant')) {
            testAssistantService.updateModelConfig();
            vscode.window.showInformationMessage('测试助手配置已更新');
        }
    });

    // 监听文件变更
    const fileChangeListener = vscode.workspace.onDidChangeTextDocument(event => {
        contextAnalyzer.onDocumentChange(event);
    });

    // 监听活动编辑器变更
    const editorChangeListener = vscode.window.onDidChangeActiveTextEditor(editor => {
        if (editor) {
            contextAnalyzer.onActiveEditorChange(editor);
        }
    });

    // 监听选择变更
    const selectionChangeListener = vscode.window.onDidChangeTextEditorSelection(event => {
        contextAnalyzer.onSelectionChange(event);
    });

    context.subscriptions.push(
        configChangeListener,
        fileChangeListener,
        editorChangeListener,
        selectionChangeListener
    );
}

/**
 * 分析当前代码
 */
async function analyzeCurrentCode() {
    const editorInfo = testAssistantService.getCurrentEditorInfo();
    if (!editorInfo) {
        vscode.window.showWarningMessage('请先打开一个代码文件');
        return;
    }

    // 显示进度
    await vscode.window.withProgress({
        location: vscode.ProgressLocation.Notification,
        title: "正在分析代码...",
        cancellable: false
    }, async (progress) => {
        progress.report({ increment: 0 });

        try {
            // 获取上下文信息
            const context = await contextAnalyzer.getContextForCode(editorInfo.code, editorInfo.language);
            
            progress.report({ increment: 30 });

            // 分析代码
            const result = await testAssistantService.analyzeCode(
                editorInfo.code, 
                editorInfo.language, 
                context
            );

            progress.report({ increment: 70 });

            if (result.success) {
                // 显示结果
                await showAnalysisResult(result.analysis || '分析完成', result.suggestions);
                
                // 保存到历史
                historyManager.addAnalysisHistory({
                    code: editorInfo.code,
                    language: editorInfo.language,
                    result: result,
                    timestamp: new Date()
                });
            } else {
                vscode.window.showErrorMessage(`代码分析失败: ${result.error}`);
            }

            progress.report({ increment: 100 });
        } catch (error) {
            vscode.window.showErrorMessage(`分析过程中发生错误: ${error}`);
        }
    });
}

/**
 * 生成测试建议
 */
async function generateTestSuggestions() {
    const editorInfo = testAssistantService.getCurrentEditorInfo();
    if (!editorInfo) {
        vscode.window.showWarningMessage('请先打开一个代码文件');
        return;
    }

    // 询问测试类型
    const testType = await vscode.window.showQuickPick([
        { label: '单元测试', value: 'unit' },
        { label: '集成测试', value: 'integration' },
        { label: '功能测试', value: 'functional' },
        { label: '性能测试', value: 'performance' },
        { label: '自动选择', value: 'auto' }
    ], {
        placeHolder: '选择测试类型'
    });

    if (!testType) {
        return;
    }

    await vscode.window.withProgress({
        location: vscode.ProgressLocation.Notification,
        title: "正在生成测试建议...",
        cancellable: false
    }, async (progress) => {
        progress.report({ increment: 0 });

        try {
            const result = await testAssistantService.generateTestSuggestions(
                editorInfo.code, 
                editorInfo.language, 
                testType.value === 'auto' ? undefined : testType.value
            );

            progress.report({ increment: 70 });

            if (result.success) {
                await showTestSuggestionResult(result);
                
                // 保存到历史
                historyManager.addTestHistory({
                    code: editorInfo.code,
                    language: editorInfo.language,
                    testType: testType.value,
                    result: result,
                    timestamp: new Date()
                });
            } else {
                vscode.window.showErrorMessage(`测试建议生成失败: ${result.error}`);
            }

            progress.report({ increment: 100 });
        } catch (error) {
            vscode.window.showErrorMessage(`生成测试建议时发生错误: ${error}`);
        }
    });
}

/**
 * 修复错误
 */
async function fixError() {
    const editorInfo = testAssistantService.getCurrentEditorInfo();
    if (!editorInfo) {
        vscode.window.showWarningMessage('请先打开一个代码文件');
        return;
    }

    // 获取错误信息
    const errorMessage = await vscode.window.showInputBox({
        prompt: '请输入错误信息或描述',
        placeHolder: '例如：TypeError: Cannot read property...'
    });

    if (!errorMessage) {
        return;
    }

    await vscode.window.withProgress({
        location: vscode.ProgressLocation.Notification,
        title: "正在分析错误并生成修复建议...",
        cancellable: false
    }, async (progress) => {
        progress.report({ increment: 0 });

        try {
            const result = await testAssistantService.fixError(
                editorInfo.code, 
                errorMessage, 
                editorInfo.language
            );

            progress.report({ increment: 70 });

            if (result.success) {
                await showErrorFixResult(result);
            } else {
                vscode.window.showErrorMessage(`错误修复建议生成失败: ${result.error}`);
            }

            progress.report({ increment: 100 });
        } catch (error) {
            vscode.window.showErrorMessage(`生成错误修复建议时发生错误: ${error}`);
        }
    });
}

/**
 * 接口测试
 */
async function testInterface() {
    const editorInfo = testAssistantService.getCurrentEditorInfo();
    if (!editorInfo) {
        vscode.window.showWarningMessage('请先打开一个代码文件');
        return;
    }

    await vscode.window.withProgress({
        location: vscode.ProgressLocation.Notification,
        title: "正在生成接口测试建议...",
        cancellable: false
    }, async (progress) => {
        progress.report({ increment: 0 });

        try {
            const result = await testAssistantService.suggestInterfaceTest(
                editorInfo.code, 
                editorInfo.language
            );

            progress.report({ increment: 70 });

            if (result.success) {
                await showTestSuggestionResult(result);
            } else {
                vscode.window.showErrorMessage(`接口测试建议生成失败: ${result.error}`);
            }

            progress.report({ increment: 100 });
        } catch (error) {
            vscode.window.showErrorMessage(`生成接口测试建议时发生错误: ${error}`);
        }
    });
}

/**
 * 准备测试数据
 */
async function prepareTestData() {
    const editorInfo = testAssistantService.getCurrentEditorInfo();
    if (!editorInfo) {
        vscode.window.showWarningMessage('请先打开一个代码文件');
        return;
    }

    // 询问数据类型
    const dataType = await vscode.window.showQuickPick([
        { label: '正常数据', value: 'normal' },
        { label: '边界数据', value: 'boundary' },
        { label: '异常数据', value: 'exception' },
        { label: '性能测试数据', value: 'performance' },
        { label: '全部类型', value: 'all' }
    ], {
        placeHolder: '选择测试数据类型'
    });

    if (!dataType) {
        return;
    }

    await vscode.window.withProgress({
        location: vscode.ProgressLocation.Notification,
        title: "正在准备测试数据...",
        cancellable: false
    }, async (progress) => {
        progress.report({ increment: 0 });

        try {
            const result = await testAssistantService.prepareTestData(
                editorInfo.code, 
                editorInfo.language, 
                dataType.value === 'all' ? undefined : dataType.value
            );

            progress.report({ increment: 70 });

            if (result.success) {
                await showTestDataResult(result.content || '测试数据准备完成');
            } else {
                vscode.window.showErrorMessage(`测试数据准备失败: ${result.error}`);
            }

            progress.report({ increment: 100 });
        } catch (error) {
            vscode.window.showErrorMessage(`准备测试数据时发生错误: ${error}`);
        }
    });
}

/**
 * 显示分析结果
 */
async function showAnalysisResult(analysis: string, suggestions?: string[]) {
    const document = await vscode.workspace.openTextDocument({
        content: `# 代码分析结果\n\n${analysis}\n\n${suggestions ? '## 建议\n' + suggestions.join('\n') : ''}`,
        language: 'markdown'
    });
    await vscode.window.showTextDocument(document);
}

/**
 * 显示测试建议结果
 */
async function showTestSuggestionResult(result: any) {
    let content = '# 测试建议\n\n';
    
    if (result.test_cases && result.test_cases.length > 0) {
        content += '## 测试用例\n' + result.test_cases.join('\n') + '\n\n';
    }
    
    if (result.test_code) {
        content += '## 测试代码\n```\n' + result.test_code + '\n```\n\n';
    }
    
    if (result.suggestions && result.suggestions.length > 0) {
        content += '## 其他建议\n' + result.suggestions.join('\n');
    }

    const document = await vscode.workspace.openTextDocument({
        content: content,
        language: 'markdown'
    });
    await vscode.window.showTextDocument(document);
}

/**
 * 显示错误修复结果
 */
async function showErrorFixResult(result: any) {
    let content = '# 错误修复建议\n\n';
    
    if (result.analysis) {
        content += '## 错误分析\n' + result.analysis + '\n\n';
    }
    
    if (result.fix_suggestions && result.fix_suggestions.length > 0) {
        content += '## 修复建议\n' + result.fix_suggestions.join('\n') + '\n\n';
    }
    
    if (result.fixed_code) {
        content += '## 修复后的代码\n```\n' + result.fixed_code + '\n```';
    }

    const document = await vscode.workspace.openTextDocument({
        content: content,
        language: 'markdown'
    });
    await vscode.window.showTextDocument(document);
}

/**
 * 显示测试数据结果
 */
async function showTestDataResult(content: string) {
    const document = await vscode.workspace.openTextDocument({
        content: `# 测试数据\n\n${content}`,
        language: 'markdown'
    });
    await vscode.window.showTextDocument(document);
}

/**
 * 插件停用函数
 */
export function deactivate() {
    console.log('测试助手插件正在停用...');
    
    // 清理资源
    if (testAssistantService) {
        testAssistantService.dispose();
    }
    
    if (contextAnalyzer) {
        contextAnalyzer.dispose();
    }
    
    if (historyManager) {
        historyManager.dispose();
    }
    
    console.log('测试助手插件已停用');
}
