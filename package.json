{"name": "test-assistant", "displayName": "测试助手", "description": "专为测试人员打造的智能代码理解测试助手，集成企业私有大模型", "version": "1.0.0", "publisher": "your-company", "engines": {"vscode": "^1.74.0"}, "categories": ["Testing", "Other"], "keywords": ["testing", "ai", "code-analysis", "enterprise", "assistant"], "activationEvents": ["onStartupFinished"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "testAssistant.openChat", "title": "打开测试助手", "category": "测试助手"}, {"command": "testAssistant.analyzeCode", "title": "分析代码", "category": "测试助手"}, {"command": "testAssistant.generateTest", "title": "生成测试建议", "category": "测试助手"}, {"command": "testAssistant.fixError", "title": "修复错误", "category": "测试助手"}, {"command": "testAssistant.testInterface", "title": "接口测试", "category": "测试助手"}, {"command": "testAssistant.prepareTestData", "title": "准备测试数据", "category": "测试助手"}, {"command": "testAssistant.openSettings", "title": "配置设置", "category": "测试助手"}], "menus": {"editor/context": [{"when": "editorHasSelection", "command": "testAssistant.analyzeCode", "group": "testAssistant@1"}, {"when": "editorHasSelection", "command": "testAssistant.generateTest", "group": "testAssistant@2"}, {"when": "editorHasSelection", "command": "testAssistant.fixError", "group": "testAssistant@3"}], "explorer/context": [{"when": "resourceExtname =~ /\\.(js|ts|py|java|cs|cpp|c|php|rb|go)$/", "command": "testAssistant.analyzeCode", "group": "testAssistant@1"}]}, "viewsContainers": {"activitybar": [{"id": "testAssistant", "title": "测试助手", "icon": "$(beaker)"}]}, "views": {"testAssistant": [{"id": "testAssistantChat", "name": "对话面板", "type": "webview"}, {"id": "testAssistantHistory", "name": "历史记录"}]}, "configuration": {"title": "测试助手", "properties": {"testAssistant.enterprise.url": {"type": "string", "default": "", "description": "企业模型服务URL"}, "testAssistant.enterprise.appId": {"type": "string", "default": "", "description": "企业应用ID"}, "testAssistant.enterprise.username": {"type": "string", "default": "", "description": "用户名"}, "testAssistant.enterprise.apiSecret": {"type": "string", "default": "", "description": "API密钥"}, "testAssistant.enterprise.timeout": {"type": "number", "default": 30, "description": "请求超时时间（秒）"}, "testAssistant.enterprise.maxRetries": {"type": "number", "default": 3, "description": "最大重试次数"}, "testAssistant.chat.maxHistory": {"type": "number", "default": 50, "description": "最大对话历史记录数"}, "testAssistant.context.maxFileSize": {"type": "number", "default": 1048576, "description": "最大文件大小（字节）"}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "16.x", "typescript": "^4.9.4"}, "dependencies": {"axios": "^1.6.0", "crypto": "^1.0.1"}}