import requests
import hashlib
import json
import time
from typing import Dict, Any, Optional
from .base_model_service import BaseModelService


class EnterpriseModelService(BaseModelService):
    """企业内部模型服务类"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """初始化企业模型服务
        
        Args:
            config: 企业模型配置，包含 url, app_id, username, api_secret 等
        """
        super().__init__("enterprise", config)
        self.url = self.config.get('url', 'http://inmmcuat.xiaopuuat.com:13600/reflect/mmc/llm/app/chat/completion')
        self.app_id = self.config.get('app_id', 'app_20250709104215697_908735')
        self.username = self.config.get('username', 'uatgw06541')
        self.api_secret = self.config.get('api_secret', 'edf35acd3d9c4f66b82a1398e3ec769e')
        self.timeout = self.config.get('timeout', 30)
        self.max_retries = self.config.get('max_retries', 3)
        
    def initialize(self) -> bool:
        """初始化企业模型服务"""
        try:
            # 验证必要的配置参数
            if not all([self.url, self.app_id, self.username, self.api_secret]):
                return False
            
            # 测试连接
            test_response = self._test_connection()
            self.is_initialized = test_response
            return self.is_initialized
            
        except Exception as e:
            print(f"企业模型服务初始化失败: {str(e)}")
            return False
    
    def is_available(self) -> bool:
        """检查企业模型服务是否可用"""
        if not self.is_initialized:
            return self.initialize()
        return self._test_connection()
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取企业模型信息"""
        return {
            "model_type": "enterprise",
            "model_name": "企业内部大模型",
            "provider": "Enterprise Internal",
            "url": self.url,
            "app_id": self.app_id,
            "username": self.username,
            "is_available": self.is_initialized and self.is_available() if self.is_initialized else False,
            "description": "企业内部部署的大语言模型服务"
        }
    
    def _test_connection(self) -> bool:
        """测试企业模型连接"""
        try:
            test_query = "测试连接"
            response = self._make_request(test_query)
            return response.get('success', False)
        except Exception:
            return False
    
    def _md5_encrypt(self, text: str) -> str:
        """MD5加密"""
        md5_obj = hashlib.md5(text.encode('utf8'))
        return md5_obj.hexdigest()
    
    def _generate_signature(self, query: str) -> str:
        """生成签名"""
        signature_text = self.username + query + self.api_secret
        return self._md5_encrypt(signature_text)
    
    def _make_request(self, query: str) -> Dict[str, Any]:
        """发送请求到企业模型"""
        data = {
            "query": query,
            "history": [],
            "stream": False,  # 改为非流式以简化处理
            "chat_type": "sync",
            "app_id": self.app_id,
            "username": self.username
        }
        
        signature = self._generate_signature(query)
        
        headers = {
            "Content-Type": "application/json; charset=UTF-8",
            "x-api-key": "UTMP",
            "x-signature": signature
        }
        
        for attempt in range(self.max_retries):
            try:
                response = requests.post(
                    self.url,
                    headers=headers,
                    json=data,
                    timeout=self.timeout
                )
                
                if response.status_code == 200:
                    # 处理企业模型的响应格式
                    response_text = response.text
                    
                    # 如果是流式响应，需要解析SSE格式
                    if "event:" in response_text and "data:" in response_text:
                        content = self._parse_sse_response(response_text)
                    else:
                        # 非流式响应，直接解析JSON
                        try:
                            response_json = response.json()
                            content = response_json.get('content', response_text)
                        except:
                            content = response_text
                    
                    return {
                        'success': True,
                        'content': content,
                        'raw_response': response_text
                    }
                else:
                    error_msg = f"HTTP {response.status_code}: {response.text}"
                    if attempt == self.max_retries - 1:
                        return {
                            'success': False,
                            'error': error_msg,
                            'content': None
                        }
                    time.sleep(1)  # 重试前等待
                    
            except requests.exceptions.Timeout:
                error_msg = f"请求超时 (尝试 {attempt + 1}/{self.max_retries})"
                if attempt == self.max_retries - 1:
                    return {
                        'success': False,
                        'error': error_msg,
                        'content': None
                    }
                time.sleep(1)
                
            except requests.exceptions.ConnectionError:
                error_msg = f"连接失败 (尝试 {attempt + 1}/{self.max_retries})"
                if attempt == self.max_retries - 1:
                    return {
                        'success': False,
                        'error': error_msg,
                        'content': None
                    }
                time.sleep(2)
                
            except Exception as e:
                error_msg = f"请求异常: {str(e)}"
                if attempt == self.max_retries - 1:
                    return {
                        'success': False,
                        'error': error_msg,
                        'content': None
                    }
                time.sleep(1)
        
        return {
            'success': False,
            'error': "达到最大重试次数",
            'content': None
        }
    
    def _parse_sse_response(self, response_text: str) -> str:
        """解析SSE流式响应"""
        try:
            lines = response_text.strip().split('\n')
            content_parts = []
            
            for line in lines:
                if line.startswith('data: '):
                    data_json = line[6:]  # 移除 'data: ' 前缀
                    try:
                        data = json.loads(data_json)
                        if 'body' in data and 'data' in data['body']:
                            body_data = data['body']['data']
                            if 'content' in body_data:
                                content_parts.append(body_data['content'])
                    except json.JSONDecodeError:
                        continue
            
            return ''.join(content_parts)
        except Exception:
            return response_text
    
    def _call_llm(self, prompt: str) -> Dict[str, Any]:
        """调用企业内部大语言模型
        
        Args:
            prompt: 提示词
            
        Returns:
            Dict[str, Any]: 模型返回的JSON结果
        """
        try:
            if not self.is_available():
                return self._create_error_response("企业模型服务不可用")
            
            # 发送请求
            response = self._make_request(prompt)
            
            if not response.get('success', False):
                error_msg = response.get('error', '未知错误')
                return self._create_error_response(error_msg)
            
            # 解析响应内容
            content = response.get('content', '')
            if not content:
                return self._create_error_response("模型返回空内容")
            
            # 解析为JSON格式
            return self._parse_llm_response(content)
            
        except Exception as e:
            return self._create_error_response(f"调用企业模型失败: {str(e)}")
    
    def set_config(self, config: Dict[str, Any]):
        """更新企业模型配置"""
        self.config.update(config)
        self.url = self.config.get('url', self.url)
        self.app_id = self.config.get('app_id', self.app_id)
        self.username = self.config.get('username', self.username)
        self.api_secret = self.config.get('api_secret', self.api_secret)
        self.timeout = self.config.get('timeout', self.timeout)
        self.max_retries = self.config.get('max_retries', self.max_retries)
        
        # 重新初始化
        self.is_initialized = False
        self.initialize()
    
    def get_config(self) -> Dict[str, Any]:
        """获取当前配置（不包含敏感信息）"""
        return {
            'url': self.url,
            'app_id': self.app_id,
            'username': self.username,
            'timeout': self.timeout,
            'max_retries': self.max_retries,
            'model_type': self.model_type
        }